/*******************************************************************************
 * Size: 18 px
 * Bpp: 4
 * Opts: --bpp 4 --size 18 --no-compress --font SourceHanSansCN-Medium.ttf --symbols TCM --format lvgl -o font_source_han_sans_medium_18.c
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef FONT_SOURCE_HAN_SANS_MEDIUM_18
#define FONT_SOURCE_HAN_SANS_MEDIUM_18 1
#endif

#if FONT_SOURCE_HAN_SANS_MEDIUM_18

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0043 "C" */
    0x0, 0x6, 0xce, 0xfc, 0x40, 0x0, 0x1d, 0xff,
    0xdd, 0xff, 0x70, 0xc, 0xfc, 0x20, 0x2, 0xb1,
    0x5, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x70,
    0x0, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x30, 0x0, 0x0, 0x0, 0xe, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x70, 0x0, 0x0,
    0x0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xfc, 0x20, 0x2, 0xc7, 0x0, 0x2d, 0xff, 0xed,
    0xff, 0x90, 0x0, 0x7, 0xdf, 0xfc, 0x50, 0x0,

    /* U+004D "M" */
    0x4f, 0xf5, 0x0, 0x0, 0x6, 0xff, 0x34, 0xff,
    0xb0, 0x0, 0x0, 0xcf, 0xf3, 0x4f, 0xff, 0x10,
    0x0, 0x2f, 0xef, 0x34, 0xfa, 0xf7, 0x0, 0x7,
    0xfa, 0xf3, 0x4f, 0x8e, 0xd0, 0x0, 0xdc, 0xaf,
    0x34, 0xf9, 0x8f, 0x20, 0x3f, 0x6b, 0xf3, 0x4f,
    0xa2, 0xf8, 0x9, 0xf1, 0xbf, 0x34, 0xfa, 0xc,
    0xe0, 0xea, 0xb, 0xf3, 0x4f, 0xa0, 0x6f, 0x7f,
    0x40, 0xbf, 0x34, 0xfa, 0x1, 0xff, 0xe0, 0xb,
    0xf3, 0x4f, 0xa0, 0xa, 0xf9, 0x0, 0xbf, 0x34,
    0xfa, 0x0, 0x4c, 0x20, 0xb, 0xf3, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xbf, 0x30,

    /* U+0054 "T" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0x75, 0xbb, 0xbe,
    0xfe, 0xbb, 0xb5, 0x0, 0x0, 0x9f, 0x80, 0x0,
    0x0, 0x0, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x80, 0x0, 0x0, 0x0, 0x9, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0,
    0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80,
    0x0, 0x0, 0x0, 0x9, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x80, 0x0, 0x0, 0x0, 0x9, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 186, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 72, .adv_w = 239, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 157, .adv_w = 176, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0xa, 0x11
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 67, .range_length = 18, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 3, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 1, 0, 2
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    -10, -5, -8, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 2,
    .right_class_cnt     = 2,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_source_han_sans_medium_18 = {
#else
lv_font_t font_source_han_sans_medium_18 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 13,          /*The maximum line height required by the font*/
    .base_line = 0,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_SOURCE_HAN_SANS_MEDIUM_18*/

