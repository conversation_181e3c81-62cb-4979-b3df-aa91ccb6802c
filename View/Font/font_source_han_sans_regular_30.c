/*******************************************************************************
 * Size: 30 px
 * Bpp: 4
 * Opts: --bpp 4 --size 30 --no-compress --font SourceHanSansCN-Regular.ttf --range 32-127,20142,20851,20986,21035,21151,21378,21462,21495,21516,22411,22797,23567,24230,24453,24674,25195,25551,26080,26368,26412,26426,27493,28040,29256,29575,30721,30830,32447,32622,35748,35774,35782,36827,36947,39057 --format lvgl -o font_source_han_sans_regular_30.c
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef FONT_SOURCE_HAN_SANS_REGULAR_30
#define FONT_SOURCE_HAN_SANS_REGULAR_30 1
#endif

#if FONT_SOURCE_HAN_SANS_REGULAR_30

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x8, 0xff, 0x30, 0x8f, 0xf2, 0x7, 0xff, 0x20,
    0x7f, 0xf2, 0x7, 0xff, 0x10, 0x6f, 0xf1, 0x6,
    0xff, 0x0, 0x5f, 0xf0, 0x5, 0xff, 0x0, 0x4f,
    0xf0, 0x4, 0xff, 0x0, 0x3f, 0xe0, 0x3, 0xfe,
    0x0, 0x2f, 0xd0, 0x2, 0xfd, 0x0, 0x2f, 0xc0,
    0x0, 0x11, 0x0, 0x2, 0x10, 0x9, 0xff, 0x41,
    0xff, 0xfb, 0xf, 0xff, 0xb0, 0x6e, 0xd2,

    /* U+0022 "\"" */
    0x4f, 0xf9, 0x0, 0x6f, 0xf7, 0x4f, 0xf8, 0x0,
    0x6f, 0xf7, 0x4f, 0xf8, 0x0, 0x5f, 0xf6, 0x3f,
    0xf7, 0x0, 0x5f, 0xf6, 0x1f, 0xf6, 0x0, 0x3f,
    0xf4, 0xf, 0xf4, 0x0, 0x1f, 0xf2, 0xe, 0xf3,
    0x0, 0xf, 0xf1, 0xd, 0xf2, 0x0, 0xe, 0xf0,
    0x9, 0xc0, 0x0, 0xa, 0xb0,

    /* U+0023 "#" */
    0x0, 0x0, 0x5f, 0x60, 0x0, 0x2f, 0xa0, 0x0,
    0x0, 0x7, 0xf4, 0x0, 0x4, 0xf8, 0x0, 0x0,
    0x0, 0x9f, 0x20, 0x0, 0x6f, 0x60, 0x0, 0x0,
    0xb, 0xf0, 0x0, 0x8, 0xf4, 0x0, 0x0, 0x0,
    0xee, 0x0, 0x0, 0xaf, 0x20, 0x0, 0x0, 0xf,
    0xc0, 0x0, 0xc, 0xf0, 0x0, 0x2e, 0xee, 0xff,
    0xee, 0xee, 0xff, 0xee, 0x92, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x5, 0xf6, 0x0,
    0x1, 0xfa, 0x0, 0x0, 0x0, 0x7f, 0x40, 0x0,
    0x3f, 0x80, 0x0, 0x0, 0x9, 0xf2, 0x0, 0x5,
    0xf6, 0x0, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x7f,
    0x40, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x9, 0xf2,
    0x0, 0xb, 0xee, 0xff, 0xee, 0xee, 0xff, 0xee,
    0xe0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x3, 0xf9, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0x5f, 0x70, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x7, 0xf5, 0x0, 0x3, 0xf9, 0x0, 0x0, 0x0,
    0x9f, 0x30, 0x0, 0x5f, 0x70, 0x0, 0x0, 0xa,
    0xf1, 0x0, 0x7, 0xf5, 0x0, 0x0, 0x0, 0xcf,
    0x0, 0x0, 0x9f, 0x30, 0x0, 0x0, 0xe, 0xd0,
    0x0, 0xa, 0xf2, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xcf, 0xb6, 0x10, 0x0, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x3f, 0xff, 0xdb, 0xcf,
    0xff, 0x90, 0x0, 0xdf, 0xf6, 0x0, 0x0, 0x9f,
    0xa0, 0x3, 0xff, 0x90, 0x0, 0x0, 0x4, 0x0,
    0x5, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xc4,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xc3,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xef, 0xff, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf9, 0x4, 0x80, 0x0, 0x0, 0x0,
    0x6f, 0xf6, 0xd, 0xfd, 0x40, 0x0, 0x4, 0xff,
    0xf0, 0x9, 0xff, 0xfe, 0xbb, 0xdf, 0xff, 0x50,
    0x0, 0x4c, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x27, 0xcf, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x70, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x3b, 0xef, 0xc6, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xf8, 0x0, 0x0, 0x0, 0x4, 0xff, 0xdc,
    0xff, 0x90, 0x0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x2e, 0xf4, 0x0,
    0x0, 0x0, 0x7f, 0x60, 0x0, 0x0, 0x0, 0x6f,
    0xb0, 0x0, 0x7, 0xfb, 0x0, 0x0, 0x1, 0xed,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x60, 0x0, 0x2,
    0xff, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0xff, 0x10, 0x0,
    0x2f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x30,
    0x0, 0x0, 0xff, 0x20, 0x0, 0xaf, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0xff,
    0x10, 0x3, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x60, 0x0, 0x2, 0xff, 0x0, 0xc, 0xf1,
    0x0, 0x8e, 0xfd, 0x80, 0x0, 0x6f, 0xb0, 0x0,
    0x7, 0xfb, 0x0, 0x5f, 0x80, 0xc, 0xfe, 0xce,
    0xfc, 0x0, 0xe, 0xf6, 0x0, 0x2e, 0xf4, 0x0,
    0xde, 0x0, 0x8f, 0xc0, 0x1, 0xcf, 0x70, 0x4,
    0xff, 0xcb, 0xff, 0x90, 0x6, 0xf6, 0x0, 0xff,
    0x20, 0x0, 0x2f, 0xe0, 0x0, 0x3b, 0xef, 0xc6,
    0x0, 0xe, 0xd0, 0x4, 0xfc, 0x0, 0x0, 0xd,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x50,
    0x6, 0xfa, 0x0, 0x0, 0xa, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfc, 0x0, 0x7, 0xf9, 0x0,
    0x0, 0x9, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf3, 0x0, 0x7, 0xf9, 0x0, 0x0, 0x9, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xa0, 0x0, 0x6,
    0xfa, 0x0, 0x0, 0xa, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x20, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0xd, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xf9, 0x0,
    0x0, 0x0, 0xff, 0x20, 0x0, 0x3f, 0xe0, 0x0,
    0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x8f,
    0xc0, 0x1, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x6f,
    0x70, 0x0, 0x0, 0x0, 0xc, 0xfe, 0xcf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8e, 0xfd, 0x80, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x5, 0xce, 0xfc, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x81, 0x9,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0,
    0x0, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf6, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x50, 0x0, 0x1f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf7, 0x0, 0x9, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x7, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x2a,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x1, 0xdd,
    0x60, 0x8, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x6f, 0xf2, 0x8, 0xff, 0xb1, 0xef, 0xd1, 0x0,
    0x0, 0xc, 0xfc, 0x3, 0xff, 0xb0, 0x3, 0xff,
    0xc0, 0x0, 0x3, 0xff, 0x50, 0xaf, 0xf2, 0x0,
    0x5, 0xff, 0xc1, 0x0, 0xdf, 0xd0, 0xd, 0xfd,
    0x0, 0x0, 0x7, 0xff, 0xd2, 0x7f, 0xf4, 0x0,
    0xdf, 0xc0, 0x0, 0x0, 0x7, 0xff, 0xef, 0xfa,
    0x0, 0xb, 0xff, 0x10, 0x0, 0x0, 0x5, 0xff,
    0xff, 0x10, 0x0, 0x5f, 0xfc, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xfd, 0x40, 0x0, 0xcf, 0xfe, 0x74,
    0x47, 0xcf, 0xfd, 0xbf, 0xff, 0xc6, 0x1, 0xbf,
    0xff, 0xff, 0xff, 0xfa, 0x10, 0x5e, 0xff, 0xa0,
    0x0, 0x4a, 0xdf, 0xed, 0x83, 0x0, 0x0, 0x6,
    0xc5,

    /* U+0027 "'" */
    0x4f, 0xf9, 0x4f, 0xf8, 0x4f, 0xf8, 0x3f, 0xf7,
    0x1f, 0xf6, 0xf, 0xf4, 0xe, 0xf3, 0xd, 0xf2,
    0x9, 0xc0,

    /* U+0028 "(" */
    0x0, 0x0, 0x1c, 0x40, 0x0, 0x9, 0xf8, 0x0,
    0x2, 0xff, 0x10, 0x0, 0xaf, 0x80, 0x0, 0x1f,
    0xf1, 0x0, 0x6, 0xfc, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x2f, 0xf1, 0x0, 0x6, 0xfd, 0x0, 0x0,
    0xaf, 0xa0, 0x0, 0xd, 0xf7, 0x0, 0x0, 0xff,
    0x50, 0x0, 0x1f, 0xf3, 0x0, 0x2, 0xff, 0x20,
    0x0, 0x3f, 0xf1, 0x0, 0x3, 0xff, 0x0, 0x0,
    0x3f, 0xf1, 0x0, 0x2, 0xff, 0x20, 0x0, 0x1f,
    0xf3, 0x0, 0x0, 0xff, 0x40, 0x0, 0xd, 0xf7,
    0x0, 0x0, 0xaf, 0xa0, 0x0, 0x6, 0xfd, 0x0,
    0x0, 0x2f, 0xf1, 0x0, 0x0, 0xdf, 0x60, 0x0,
    0x7, 0xfb, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0,
    0xaf, 0x70, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x9,
    0xf7, 0x0, 0x0, 0x1c, 0x50,

    /* U+0029 ")" */
    0x3b, 0x30, 0x0, 0x6, 0xfb, 0x0, 0x0, 0xe,
    0xf4, 0x0, 0x0, 0x6f, 0xc0, 0x0, 0x0, 0xff,
    0x20, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0xff, 0x40, 0x0, 0xb, 0xf8, 0x0,
    0x0, 0x8f, 0xc0, 0x0, 0x5, 0xff, 0x0, 0x0,
    0x2f, 0xf1, 0x0, 0x1, 0xff, 0x30, 0x0, 0xf,
    0xf5, 0x0, 0x0, 0xff, 0x50, 0x0, 0xe, 0xf6,
    0x0, 0x0, 0xff, 0x50, 0x0, 0xf, 0xf5, 0x0,
    0x1, 0xff, 0x30, 0x0, 0x2f, 0xf2, 0x0, 0x5,
    0xff, 0x0, 0x0, 0x8f, 0xc0, 0x0, 0xb, 0xf9,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x4f, 0xe0, 0x0,
    0x9, 0xf9, 0x0, 0x0, 0xef, 0x30, 0x0, 0x6f,
    0xc0, 0x0, 0xd, 0xf4, 0x0, 0x6, 0xfb, 0x0,
    0x0, 0x4c, 0x30, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0xd, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xe0, 0x0, 0x0, 0x1, 0x0, 0xf, 0xf0,
    0x0, 0x10, 0xc, 0xea, 0x7f, 0xf7, 0xae, 0xb0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x17,
    0xff, 0xff, 0x71, 0x0, 0x0, 0x3, 0xff, 0xff,
    0x30, 0x0, 0x0, 0xd, 0xf9, 0x9f, 0xc0, 0x0,
    0x0, 0x6f, 0xc0, 0xc, 0xf6, 0x0, 0x0, 0x2a,
    0x10, 0x1, 0xa2, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x56, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x11, 0x11, 0x1c, 0xf7,
    0x11, 0x11, 0x10, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0,

    /* U+002C "," */
    0x1, 0xbd, 0x70, 0xa, 0xff, 0xf4, 0xa, 0xff,
    0xf8, 0x2, 0xdf, 0xf9, 0x0, 0x8, 0xf8, 0x0,
    0xc, 0xf4, 0x0, 0x5f, 0xd0, 0x7, 0xff, 0x30,
    0x3f, 0xe4, 0x0, 0x7, 0x10, 0x0,

    /* U+002D "-" */
    0x12, 0x22, 0x22, 0x22, 0xa, 0xff, 0xff, 0xff,
    0xf0, 0xaf, 0xff, 0xff, 0xff, 0x0,

    /* U+002E "." */
    0x1, 0x20, 0x4, 0xff, 0x90, 0xbf, 0xff, 0x1a,
    0xff, 0xf0, 0x2d, 0xe6, 0x0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x60, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x10, 0x0, 0x0, 0x0, 0x2, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x0, 0x0,
    0x2, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x48, 0x30,
    0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x4b, 0xef, 0xd9, 0x10, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0,
    0x7f, 0xfd, 0x53, 0x7f, 0xff, 0x20, 0x0, 0x1f,
    0xfd, 0x0, 0x0, 0x3f, 0xfb, 0x0, 0x8, 0xff,
    0x30, 0x0, 0x0, 0x8f, 0xf3, 0x0, 0xdf, 0xc0,
    0x0, 0x0, 0x2, 0xff, 0x80, 0x1f, 0xf8, 0x0,
    0x0, 0x0, 0xd, 0xfc, 0x4, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xaf, 0xe0, 0x6f, 0xf3, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x7, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x8f, 0xf1, 0x7f, 0xf2, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x27, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x7f, 0xf2, 0x6f, 0xf3, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x16, 0xff, 0x40, 0x0, 0x0, 0x0, 0x9f,
    0xf0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0xb, 0xfe,
    0x1, 0xff, 0x90, 0x0, 0x0, 0x0, 0xef, 0xb0,
    0xd, 0xfd, 0x0, 0x0, 0x0, 0x2f, 0xf7, 0x0,
    0x8f, 0xf4, 0x0, 0x0, 0x9, 0xff, 0x20, 0x1,
    0xff, 0xd1, 0x0, 0x4, 0xff, 0xb0, 0x0, 0x7,
    0xff, 0xd6, 0x48, 0xff, 0xf2, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x4,
    0xbe, 0xfd, 0x91, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x3b, 0xff, 0x40, 0x0, 0x0, 0x2a,
    0xef, 0xff, 0xf4, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x14, 0x44, 0x9f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x40, 0x0, 0x1, 0x55, 0x55, 0x9f, 0xf8,
    0x55, 0x53, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+0032 "2" */
    0x0, 0x4, 0xad, 0xfe, 0xc7, 0x0, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x2e,
    0xff, 0xb6, 0x46, 0xcf, 0xfe, 0x10, 0x7, 0xfe,
    0x30, 0x0, 0x0, 0xaf, 0xf8, 0x0, 0x6, 0x20,
    0x0, 0x0, 0x1, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xfd, 0x45, 0x66, 0x66, 0x66, 0x60, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x2b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2,

    /* U+0033 "3" */
    0x0, 0x0, 0x4a, 0xdf, 0xfd, 0x92, 0x0, 0x0,
    0x1, 0xbf, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x1,
    0xef, 0xfb, 0x64, 0x69, 0xff, 0xf7, 0x0, 0x6,
    0xd3, 0x0, 0x0, 0x4, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x70, 0x0, 0x0, 0x1, 0x34,
    0x8d, 0xff, 0x80, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xfb, 0x30, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xf9, 0x20, 0x0, 0x0, 0x0, 0x0, 0x25, 0xaf,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xd0,
    0x19, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfa, 0xc,
    0xfc, 0x20, 0x0, 0x0, 0x2e, 0xff, 0x40, 0x8f,
    0xff, 0xb7, 0x56, 0xaf, 0xff, 0x90, 0x0, 0x5e,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x6,
    0xbd, 0xff, 0xd8, 0x20, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfe, 0xbf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf6, 0xbf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xd0, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x40, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0x2f, 0xfa, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0xbf, 0xe1, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x6, 0xff, 0x40, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x1e, 0xfa, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0xaf, 0xe1, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x5, 0xff, 0x40, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x1e, 0xfc, 0x33, 0x33, 0x33, 0xdf, 0xd3, 0x32,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0,

    /* U+0035 "5" */
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xe, 0xfc, 0x66, 0x66, 0x66, 0x66, 0x0,
    0x0, 0xf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf6, 0xad, 0xfe, 0xa4, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x4f, 0xf9, 0x42, 0x37, 0xef, 0xfa, 0x0,
    0x0, 0x2, 0x20, 0x0, 0x0, 0x1e, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xc0,
    0x1, 0x80, 0x0, 0x0, 0x0, 0x9, 0xff, 0x70,
    0xc, 0xfb, 0x10, 0x0, 0x0, 0x6f, 0xfe, 0x0,
    0x9, 0xff, 0xfa, 0x75, 0x7c, 0xff, 0xf3, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x1, 0x6b, 0xdf, 0xec, 0x71, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x3, 0x9d, 0xfe, 0xc7, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0xb, 0xff, 0xe8, 0x55, 0x9f, 0xfa, 0x0, 0x6,
    0xff, 0x90, 0x0, 0x0, 0x18, 0x0, 0x0, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0x4, 0xbf,
    0xff, 0xb3, 0x0, 0x3, 0xff, 0x59, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x4f, 0xfd, 0xfb, 0x40, 0x15,
    0xef, 0xf4, 0x4, 0xff, 0xf7, 0x0, 0x0, 0x2,
    0xff, 0xc0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x13, 0xff, 0x60, 0x0, 0x0, 0x0, 0x6f,
    0xf4, 0x1f, 0xf8, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x50, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0xa, 0xff, 0x10, 0x0, 0x0, 0x7, 0xff, 0x20,
    0x4f, 0xf7, 0x0, 0x0, 0x0, 0xcf, 0xe0, 0x0,
    0xcf, 0xf3, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x2,
    0xef, 0xf8, 0x44, 0x9f, 0xfd, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x7d, 0xfe, 0xc6, 0x0, 0x0,

    /* U+0037 "7" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x38,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x36,
    0x66, 0x66, 0x66, 0x66, 0x7f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xd0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x5b, 0xef, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0xaf, 0xfb, 0x42, 0x3b, 0xff, 0x90, 0x0, 0x2f,
    0xfa, 0x0, 0x0, 0xb, 0xff, 0x10, 0x7, 0xff,
    0x20, 0x0, 0x0, 0x2f, 0xf5, 0x0, 0x8f, 0xf0,
    0x0, 0x0, 0x0, 0xff, 0x70, 0x6, 0xff, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x1f, 0xf6, 0x0,
    0x0, 0x1, 0xff, 0x10, 0x0, 0x8f, 0xf4, 0x0,
    0x0, 0x7f, 0x80, 0x0, 0x0, 0x9f, 0xf9, 0x10,
    0x2f, 0xd0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xae,
    0xd1, 0x0, 0x0, 0x3, 0xef, 0x88, 0xff, 0xff,
    0x70, 0x0, 0x3, 0xff, 0x50, 0x0, 0x6e, 0xff,
    0xa0, 0x0, 0xdf, 0x90, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x4f, 0xf1, 0x0, 0x0, 0x0, 0xd, 0xfe,
    0x8, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf2,
    0x9f, 0xe0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x37,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x8f, 0xf1, 0x1f,
    0xfc, 0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x7f,
    0xfe, 0x62, 0x23, 0x8f, 0xff, 0x30, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0, 0x28,
    0xce, 0xfe, 0xc7, 0x10, 0x0,

    /* U+0039 "9" */
    0x0, 0x2, 0x9d, 0xfe, 0xb5, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x5,
    0xff, 0xd6, 0x34, 0x9f, 0xfd, 0x0, 0x0, 0xef,
    0xd1, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x5f, 0xf4,
    0x0, 0x0, 0x0, 0x9f, 0xf1, 0x8, 0xff, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x60, 0xaf, 0xe0, 0x0,
    0x0, 0x0, 0xd, 0xfa, 0xa, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xd0, 0x8f, 0xf3, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x3, 0xff, 0xb0, 0x0, 0x0,
    0xa, 0xff, 0xf0, 0xc, 0xff, 0xa3, 0x1, 0x5d,
    0xfd, 0xff, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xf5,
    0x9f, 0xf0, 0x0, 0x7, 0xdf, 0xff, 0x92, 0xb,
    0xfe, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0xdf,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x4,
    0xb1, 0x0, 0x0, 0x3e, 0xff, 0x10, 0x1, 0xef,
    0xf9, 0x66, 0xaf, 0xff, 0x50, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x1, 0x8d,
    0xff, 0xd8, 0x10, 0x0, 0x0,

    /* U+003A ":" */
    0x2d, 0xe6, 0xa, 0xff, 0xf0, 0xbf, 0xff, 0x15,
    0xff, 0xa0, 0x1, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x20,
    0x4, 0xff, 0x90, 0xbf, 0xff, 0x1a, 0xff, 0xf0,
    0x2d, 0xe6, 0x0,

    /* U+003B ";" */
    0x2, 0xde, 0x60, 0xa, 0xff, 0xf0, 0xb, 0xff,
    0xf1, 0x5, 0xff, 0xa0, 0x0, 0x12, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xbd, 0x70, 0xa, 0xff, 0xf4, 0xa, 0xff, 0xf8,
    0x2, 0xdf, 0xf9, 0x0, 0x8, 0xf8, 0x0, 0xc,
    0xf4, 0x0, 0x5f, 0xd0, 0x7, 0xff, 0x30, 0x3f,
    0xe4, 0x0, 0x7, 0x10, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xc8, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x9e, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x5c, 0xff, 0xff, 0x92, 0x0, 0x0,
    0x29, 0xef, 0xff, 0xb5, 0x0, 0x0, 0x5, 0xcf,
    0xff, 0xd8, 0x20, 0x0, 0x0, 0x8e, 0xff, 0xfa,
    0x40, 0x0, 0x0, 0x0, 0xd, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5b, 0xff, 0xfe, 0x82,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x8e, 0xff, 0xfc,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xff,
    0xfa, 0x40, 0x0, 0x0, 0x0, 0x0, 0x28, 0xef,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5b,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x30,

    /* U+003D "=" */
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x10, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfd, 0x71, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xfa, 0x30, 0x0, 0x0, 0x0, 0x0, 0x17,
    0xdf, 0xff, 0xd7, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x4a, 0xff, 0xff, 0xa3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6c, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xbf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xcf, 0xff, 0x70, 0x0, 0x0, 0x4, 0xaf,
    0xff, 0xf9, 0x30, 0x0, 0x2, 0x8e, 0xff, 0xfc,
    0x60, 0x0, 0x0, 0x6c, 0xff, 0xff, 0x93, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf9, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x0, 0x17, 0xdf, 0xfd, 0x91, 0x0, 0x5, 0xef,
    0xff, 0xff, 0xff, 0x40, 0x5f, 0xfd, 0x85, 0x7c,
    0xff, 0xe1, 0x1c, 0x80, 0x0, 0x0, 0xaf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xd0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x7f, 0xf6, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0xe, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xe2,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb1, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x59, 0xdf, 0xff,
    0xdb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xee, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xa5,
    0x10, 0x0, 0x3, 0x9f, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xe2, 0x0, 0x0, 0x0, 0x7f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfc,
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0, 0x1e,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xd0, 0x0, 0x8f, 0xb0, 0x0, 0x0,
    0x6, 0xce, 0xd5, 0x1b, 0x70, 0x0, 0xd, 0xf2,
    0x1, 0xff, 0x30, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xbf, 0x70, 0x0, 0x9, 0xf6, 0x7, 0xfb, 0x0,
    0x0, 0xb, 0xfe, 0x50, 0x2c, 0xff, 0x40, 0x0,
    0x6, 0xf9, 0xc, 0xf6, 0x0, 0x0, 0x6f, 0xe1,
    0x0, 0x2, 0xff, 0x10, 0x0, 0x4, 0xfa, 0xf,
    0xf1, 0x0, 0x0, 0xef, 0x60, 0x0, 0x3, 0xfe,
    0x0, 0x0, 0x4, 0xfa, 0x2f, 0xe0, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x5,
    0xf9, 0x4f, 0xc0, 0x0, 0x8, 0xfa, 0x0, 0x0,
    0x9, 0xf8, 0x0, 0x0, 0x8, 0xf6, 0x4f, 0xb0,
    0x0, 0xa, 0xf9, 0x0, 0x0, 0xb, 0xf5, 0x0,
    0x0, 0xd, 0xf2, 0x4f, 0xb0, 0x0, 0xa, 0xf9,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0x4f, 0xd0,
    0x2f, 0xc0, 0x0, 0x8, 0xfd, 0x0, 0x0, 0x9f,
    0xf4, 0x0, 0x1, 0xdf, 0x50, 0x1f, 0xf0, 0x0,
    0x3, 0xff, 0xa3, 0x4c, 0xfb, 0xfb, 0x0, 0x3d,
    0xf9, 0x0, 0xd, 0xf3, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x61, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x9,
    0xf8, 0x0, 0x0, 0x8, 0xef, 0xc4, 0x0, 0x3b,
    0xff, 0xb4, 0x0, 0x0, 0x3, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xa4, 0x10, 0x0, 0x4,
    0x9f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x9f, 0xff, 0xfe, 0xde, 0xff, 0xfe, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6a, 0xde,
    0xff, 0xda, 0x50, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x9, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xed,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfa, 0x8f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x53, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf1, 0xe, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x0, 0xaf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x70, 0x5,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2,
    0x0, 0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfd, 0x0, 0x0, 0xbf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x80, 0x0, 0x6, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x1f, 0xf8,
    0x0, 0x0, 0x0, 0xa, 0xfe, 0x0, 0x0, 0x0,
    0xcf, 0xe0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0xa,
    0xfe, 0x33, 0x33, 0x33, 0x33, 0xdf, 0xe0, 0x0,
    0x0, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x40, 0x0, 0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf9, 0x0, 0xb, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xe0, 0x0, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x40, 0x5f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0xb,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xe0,

    /* U+0042 "B" */
    0xff, 0xff, 0xff, 0xff, 0xda, 0x60, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0xff, 0xd3, 0x33, 0x34, 0x7b, 0xff, 0xf4, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x5f, 0xfc, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0,
    0xff, 0xc0, 0x0, 0x1, 0x39, 0xff, 0xc0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x70, 0x0,
    0xff, 0xd3, 0x33, 0x33, 0x58, 0xef, 0xfb, 0x10,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xa0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf4,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf4,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf1,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xa0,
    0xff, 0xd4, 0x44, 0x44, 0x6a, 0xff, 0xfd, 0x10,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xec, 0x83, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x28, 0xce, 0xfe, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x1a, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xc9, 0x89,
    0xdf, 0xff, 0x30, 0x0, 0x1d, 0xff, 0xc3, 0x0,
    0x0, 0x4, 0xec, 0x0, 0x0, 0x9f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x0, 0x2, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x19, 0x0, 0x0, 0x1e, 0xff, 0xc2, 0x0, 0x0,
    0x4, 0xdf, 0xb0, 0x0, 0x3, 0xef, 0xff, 0xc8,
    0x89, 0xdf, 0xff, 0x60, 0x0, 0x0, 0x1b, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x39, 0xdf, 0xfe, 0xb6, 0x0, 0x0,

    /* U+0044 "D" */
    0xff, 0xff, 0xff, 0xed, 0xa6, 0x10, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0xff, 0xd4, 0x44, 0x68, 0xcf, 0xff, 0xc1, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x4, 0xef, 0xfb, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0x60,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xd0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfc,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf3,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xc0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x50,
    0xff, 0xc0, 0x0, 0x0, 0x5, 0xef, 0xfa, 0x0,
    0xff, 0xd5, 0x55, 0x69, 0xdf, 0xff, 0xb0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xed, 0xa6, 0x10, 0x0, 0x0,

    /* U+0045 "E" */
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xff, 0xd6,
    0x66, 0x66, 0x66, 0x66, 0x40, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0xff, 0xd6, 0x66,
    0x66, 0x66, 0x60, 0x0, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xd6, 0x66,
    0x66, 0x66, 0x66, 0x60, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0,

    /* U+0046 "F" */
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xd6, 0x66,
    0x66, 0x66, 0x66, 0x4f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfd, 0x66, 0x66, 0x66, 0x66,
    0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x17, 0xbe, 0xfe, 0xd9, 0x20,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xd9, 0x88,
    0xae, 0xff, 0xc0, 0x0, 0xd, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x7f, 0x60, 0x0, 0x9f, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x2, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf9, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xf7, 0x3f, 0xf9, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xf7, 0x2f, 0xfa, 0x0, 0x0,
    0x0, 0x45, 0x55, 0x6f, 0xf7, 0xf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf7, 0xd, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf7, 0x9,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf7,
    0x2, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf7, 0x0, 0xaf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf7, 0x0, 0x1d, 0xff, 0xd3, 0x0, 0x0,
    0x0, 0x8f, 0xf7, 0x0, 0x2, 0xef, 0xff, 0xd9,
    0x88, 0xae, 0xff, 0xf4, 0x0, 0x0, 0x1a, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x30, 0x0, 0x0, 0x0,
    0x28, 0xce, 0xfe, 0xd9, 0x40, 0x0,

    /* U+0048 "H" */
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xd6, 0x66, 0x66, 0x66, 0x66, 0x6f, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,

    /* U+0049 "I" */
    0xff, 0xcf, 0xfc, 0xff, 0xcf, 0xfc, 0xff, 0xcf,
    0xfc, 0xff, 0xcf, 0xfc, 0xff, 0xcf, 0xfc, 0xff,
    0xcf, 0xfc, 0xff, 0xcf, 0xfc, 0xff, 0xcf, 0xfc,
    0xff, 0xcf, 0xfc, 0xff, 0xcf, 0xfc, 0xff, 0xcf,
    0xfc,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x10, 0x85,
    0x0, 0x0, 0x0, 0xff, 0xe0, 0xaf, 0xf4, 0x0,
    0x0, 0x9f, 0xf9, 0x3, 0xff, 0xfb, 0x88, 0xdf,
    0xff, 0x10, 0x4, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x1, 0x8c, 0xef, 0xd9, 0x20, 0x0,

    /* U+004B "K" */
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xc0,
    0xf, 0xfc, 0x0, 0x0, 0x0, 0x3, 0xff, 0xe1,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x1, 0xef, 0xf2,
    0x0, 0xf, 0xfc, 0x0, 0x0, 0x0, 0xdf, 0xf5,
    0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0xbf, 0xf7,
    0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x8f, 0xfa,
    0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x5f, 0xfc,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x3f, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x2e, 0xff,
    0x70, 0x0, 0x0, 0x0, 0xf, 0xfc, 0xd, 0xff,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0xff, 0xcb, 0xff,
    0xdf, 0xf9, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xb0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xd1, 0x4, 0xff, 0xb0, 0x0, 0x0, 0xf, 0xff,
    0xe2, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0, 0xff,
    0xf3, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0xf,
    0xfc, 0x0, 0x0, 0x0, 0x9f, 0xf7, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x1, 0xef, 0xf1, 0x0,
    0xf, 0xfc, 0x0, 0x0, 0x0, 0x6, 0xff, 0xa0,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x30, 0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfc, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf6, 0xf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xe0,

    /* U+004C "L" */
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfd, 0x66, 0x66, 0x66,
    0x66, 0x62, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+004D "M" */
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf5, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x5f, 0xfd, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xef, 0xf5, 0xff, 0x8f,
    0xe0, 0x0, 0x0, 0x0, 0x9, 0xf9, 0xff, 0x5f,
    0xf5, 0xef, 0x50, 0x0, 0x0, 0x0, 0xef, 0x4f,
    0xf5, 0xff, 0x69, 0xfa, 0x0, 0x0, 0x0, 0x5f,
    0xd1, 0xff, 0x5f, 0xf7, 0x4f, 0xf1, 0x0, 0x0,
    0xa, 0xf8, 0x2f, 0xf5, 0xff, 0x70, 0xef, 0x60,
    0x0, 0x0, 0xff, 0x23, 0xff, 0x5f, 0xf8, 0x8,
    0xfb, 0x0, 0x0, 0x6f, 0xc0, 0x3f, 0xf5, 0xff,
    0x80, 0x2f, 0xf1, 0x0, 0xb, 0xf7, 0x4, 0xff,
    0x5f, 0xf8, 0x0, 0xcf, 0x70, 0x1, 0xff, 0x10,
    0x4f, 0xf5, 0xff, 0x80, 0x7, 0xfc, 0x0, 0x7f,
    0xb0, 0x4, 0xff, 0x5f, 0xf8, 0x0, 0x1f, 0xf2,
    0xc, 0xf5, 0x0, 0x4f, 0xf5, 0xff, 0x80, 0x0,
    0xbf, 0x81, 0xff, 0x0, 0x4, 0xff, 0x5f, 0xf8,
    0x0, 0x5, 0xfd, 0x6f, 0xa0, 0x0, 0x4f, 0xf5,
    0xff, 0x80, 0x0, 0xf, 0xfe, 0xf4, 0x0, 0x4,
    0xff, 0x5f, 0xf8, 0x0, 0x0, 0x9f, 0xfe, 0x0,
    0x0, 0x4f, 0xf5, 0xff, 0x80, 0x0, 0x3, 0xff,
    0x80, 0x0, 0x4, 0xff, 0x5f, 0xf8, 0x0, 0x0,
    0xb, 0xc2, 0x0, 0x0, 0x4f, 0xf5, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x5f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf5,

    /* U+004E "N" */
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0xf, 0xfa,
    0xff, 0xef, 0xd0, 0x0, 0x0, 0x0, 0xf, 0xfa,
    0xff, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0xf, 0xfa,
    0xff, 0x68, 0xff, 0x10, 0x0, 0x0, 0xf, 0xfa,
    0xff, 0x71, 0xff, 0x90, 0x0, 0x0, 0xf, 0xfa,
    0xff, 0x80, 0x8f, 0xf3, 0x0, 0x0, 0xf, 0xfa,
    0xff, 0x90, 0xe, 0xfc, 0x0, 0x0, 0xf, 0xfa,
    0xff, 0x90, 0x6, 0xff, 0x50, 0x0, 0xf, 0xfa,
    0xff, 0xa0, 0x0, 0xcf, 0xe0, 0x0, 0xf, 0xfa,
    0xff, 0xa0, 0x0, 0x3f, 0xf8, 0x0, 0xf, 0xfa,
    0xff, 0xa0, 0x0, 0xa, 0xff, 0x20, 0xe, 0xfa,
    0xff, 0xa0, 0x0, 0x1, 0xff, 0xa0, 0xe, 0xfa,
    0xff, 0xa0, 0x0, 0x0, 0x8f, 0xf3, 0xd, 0xfa,
    0xff, 0xa0, 0x0, 0x0, 0xe, 0xfb, 0xc, 0xfa,
    0xff, 0xa0, 0x0, 0x0, 0x5, 0xff, 0x4b, 0xfa,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xcf, 0xca, 0xfa,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0xfa,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xfa,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xfa,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfa,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x4a, 0xdf, 0xfe, 0xb6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xb8, 0x8a, 0xef, 0xff, 0x70, 0x0, 0x0, 0x2f,
    0xff, 0xb1, 0x0, 0x0, 0x8, 0xff, 0xf5, 0x0,
    0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfe, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x70, 0x9, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0xe, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2,
    0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf5, 0x2f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf7, 0x3f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf7, 0x3f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x2f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf6, 0xf, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf4, 0xd, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf1, 0x9, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0,
    0x3, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x60, 0x0, 0xaf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xfe, 0x0, 0x0, 0x1e, 0xff, 0xb1,
    0x0, 0x0, 0x8, 0xff, 0xf4, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xb8, 0x8a, 0xef, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xdf, 0xfe,
    0xb6, 0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0xff, 0xff, 0xff, 0xfe, 0xda, 0x60, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x0, 0xff,
    0xd4, 0x44, 0x45, 0x8c, 0xff, 0xf5, 0xf, 0xfc,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x4f, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf7, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x7f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf6, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x3f, 0xfc, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xd0, 0xff, 0xd4, 0x44, 0x56, 0x8d,
    0xff, 0xe3, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0xff, 0xff, 0xff, 0xfe, 0xd9, 0x50,
    0x0, 0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x49, 0xdf, 0xfe, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xb8, 0x8a, 0xef, 0xff, 0x50, 0x0, 0x0, 0x1e,
    0xff, 0xc1, 0x0, 0x0, 0x9, 0xff, 0xf3, 0x0,
    0x0, 0x9f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfd, 0x0, 0x1, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x50, 0x7, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0xc, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf0,
    0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf4, 0x2f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf6, 0x3f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x3f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf7,
    0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf6, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf5, 0xf, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf3, 0xb, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf0,
    0x7, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xb0, 0x2, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x50, 0x0, 0x9f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfd, 0x0, 0x0, 0x1e,
    0xff, 0x90, 0x0, 0x0, 0x5, 0xff, 0xf3, 0x0,
    0x0, 0x2, 0xef, 0xfd, 0x74, 0x36, 0xbf, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b,
    0xff, 0xff, 0xd7, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xfe, 0x83, 0x10, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x54, 0x30,

    /* U+0052 "R" */
    0xff, 0xff, 0xff, 0xff, 0xeb, 0x82, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0xff, 0xd4, 0x44, 0x45, 0x7b, 0xff, 0xf8, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0x10,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x60,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x80,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x80,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x60,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x10,
    0xff, 0xc0, 0x0, 0x0, 0x38, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x93, 0x0, 0x0,
    0xff, 0xd4, 0x44, 0x4f, 0xfe, 0x0, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x1, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x7f, 0xf9, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0xe, 0xff, 0x20, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x6, 0xff, 0xb0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xdf, 0xf4, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xfc, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x50,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xe0,

    /* U+0053 "S" */
    0x0, 0x0, 0x17, 0xce, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x0,
    0x0, 0x6f, 0xff, 0xc9, 0x89, 0xdf, 0xff, 0x70,
    0x1, 0xff, 0xf4, 0x0, 0x0, 0x3, 0xcf, 0x30,
    0x5, 0xff, 0x70, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x7, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0x92, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xff, 0xff, 0xd6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4a, 0xff, 0xff, 0xd4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x18, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x7, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf5,
    0x4f, 0xfe, 0x60, 0x0, 0x0, 0x7, 0xff, 0xe0,
    0xa, 0xff, 0xff, 0xa8, 0x8a, 0xef, 0xff, 0x40,
    0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x5a, 0xdf, 0xfe, 0xb6, 0x0, 0x0,

    /* U+0054 "T" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x6, 0x66, 0x66, 0x6a, 0xff, 0xa6,
    0x66, 0x66, 0x60, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x60, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xb1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfb, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xb1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfb, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xb1, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfb, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xb1, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xb1, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfb, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xb1, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xb0, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0xf,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xa0,
    0xdf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7,
    0xa, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x40, 0x5f, 0xfc, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xe0, 0x0, 0xdf, 0xfa, 0x0, 0x0, 0x2, 0xdf,
    0xf7, 0x0, 0x3, 0xff, 0xff, 0xa8, 0x8b, 0xff,
    0xfc, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x5b, 0xdf, 0xed,
    0x93, 0x0, 0x0,

    /* U+0056 "V" */
    0xdf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x7f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xc0, 0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x70, 0xd, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x20, 0x8, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xe, 0xfd, 0x0, 0x3, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0, 0xef,
    0xe0, 0x0, 0x0, 0x0, 0x8f, 0xf2, 0x0, 0x0,
    0x9f, 0xf2, 0x0, 0x0, 0x0, 0xcf, 0xd0, 0x0,
    0x0, 0x4f, 0xf7, 0x0, 0x0, 0x1, 0xff, 0x80,
    0x0, 0x0, 0xe, 0xfc, 0x0, 0x0, 0x6, 0xff,
    0x30, 0x0, 0x0, 0x9, 0xff, 0x10, 0x0, 0xb,
    0xfe, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50, 0x0,
    0xf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xff, 0xa0,
    0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xe0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf3, 0x0, 0xdf, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf8, 0x2, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfd, 0x6, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x2b, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x7f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x60, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x2, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x3f, 0xf6, 0xe, 0xfe, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x6f, 0xf3, 0xb, 0xff, 0x10, 0x0, 0x0, 0xa,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x9f, 0xf0, 0x7,
    0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0xcf, 0xc0, 0x4, 0xff, 0x80, 0x0,
    0x0, 0x2f, 0xe9, 0xfa, 0x0, 0x0, 0x0, 0xff,
    0x90, 0x0, 0xff, 0xb0, 0x0, 0x0, 0x6f, 0xb6,
    0xfe, 0x0, 0x0, 0x3, 0xff, 0x60, 0x0, 0xdf,
    0xe0, 0x0, 0x0, 0xaf, 0x72, 0xff, 0x20, 0x0,
    0x6, 0xff, 0x30, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0xef, 0x40, 0xff, 0x60, 0x0, 0x9, 0xff, 0x0,
    0x0, 0x6f, 0xf4, 0x0, 0x2, 0xff, 0x0, 0xbf,
    0xb0, 0x0, 0xc, 0xfc, 0x0, 0x0, 0x3f, 0xf7,
    0x0, 0x7, 0xfc, 0x0, 0x7f, 0xe0, 0x0, 0xf,
    0xf9, 0x0, 0x0, 0xf, 0xfa, 0x0, 0xb, 0xf9,
    0x0, 0x3f, 0xf3, 0x0, 0x3f, 0xf5, 0x0, 0x0,
    0xc, 0xfd, 0x0, 0xe, 0xf5, 0x0, 0xf, 0xf7,
    0x0, 0x6f, 0xf2, 0x0, 0x0, 0x9, 0xff, 0x0,
    0x2f, 0xf1, 0x0, 0xb, 0xfb, 0x0, 0x9f, 0xf0,
    0x0, 0x0, 0x5, 0xff, 0x30, 0x6f, 0xd0, 0x0,
    0x7, 0xfe, 0x0, 0xbf, 0xb0, 0x0, 0x0, 0x2,
    0xff, 0x60, 0xaf, 0x90, 0x0, 0x3, 0xff, 0x20,
    0xef, 0x80, 0x0, 0x0, 0x0, 0xef, 0x90, 0xdf,
    0x50, 0x0, 0x0, 0xff, 0x51, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xbf, 0xc1, 0xff, 0x10, 0x0, 0x0,
    0xbf, 0x93, 0xff, 0x20, 0x0, 0x0, 0x0, 0x8f,
    0xf4, 0xfd, 0x0, 0x0, 0x0, 0x7f, 0xc6, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xfa, 0xf9, 0x0,
    0x0, 0x0, 0x3f, 0xf9, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf1, 0x0, 0x0,

    /* U+0058 "X" */
    0xc, 0xff, 0x30, 0x0, 0x0, 0x0, 0xe, 0xfe,
    0x0, 0x3f, 0xfc, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x60, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x0, 0xef,
    0xd0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x7f,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0x60, 0x0, 0x1e,
    0xfb, 0x0, 0x0, 0x0, 0xe, 0xfe, 0x0, 0x8,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x1,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xe1,
    0x8f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x9f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xd1, 0xef, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf6, 0x7, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xd, 0xfd, 0x0, 0xe, 0xfe, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x50, 0x0, 0x5f, 0xf8, 0x0,
    0x0, 0x0, 0xef, 0xd0, 0x0, 0x0, 0xcf, 0xf2,
    0x0, 0x0, 0x8f, 0xf4, 0x0, 0x0, 0x4, 0xff,
    0xb0, 0x0, 0x1f, 0xfc, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x40, 0xa, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x3, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf6,

    /* U+0059 "Y" */
    0xc, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xb0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf3, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x1f,
    0xfb, 0x0, 0x5, 0xff, 0x80, 0x0, 0x0, 0x8,
    0xff, 0x30, 0x0, 0xd, 0xfe, 0x0, 0x0, 0x0,
    0xef, 0xb0, 0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0,
    0x6f, 0xf4, 0x0, 0x0, 0x0, 0xdf, 0xd0, 0x0,
    0xd, 0xfc, 0x0, 0x0, 0x0, 0x5, 0xff, 0x50,
    0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0xd, 0xfc,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf3, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xab, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5,
    0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x2, 0x66, 0x66, 0x66, 0x66, 0x67, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0x76, 0x66, 0x66, 0x66, 0x66, 0x64,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+005B "[" */
    0xdf, 0xff, 0xff, 0x2d, 0xfb, 0xaa, 0xa1, 0xdf,
    0x30, 0x0, 0xd, 0xf3, 0x0, 0x0, 0xdf, 0x30,
    0x0, 0xd, 0xf3, 0x0, 0x0, 0xdf, 0x30, 0x0,
    0xd, 0xf3, 0x0, 0x0, 0xdf, 0x30, 0x0, 0xd,
    0xf3, 0x0, 0x0, 0xdf, 0x30, 0x0, 0xd, 0xf3,
    0x0, 0x0, 0xdf, 0x30, 0x0, 0xd, 0xf3, 0x0,
    0x0, 0xdf, 0x30, 0x0, 0xd, 0xf3, 0x0, 0x0,
    0xdf, 0x30, 0x0, 0xd, 0xf3, 0x0, 0x0, 0xdf,
    0x30, 0x0, 0xd, 0xf3, 0x0, 0x0, 0xdf, 0x30,
    0x0, 0xd, 0xf3, 0x0, 0x0, 0xdf, 0x30, 0x0,
    0xd, 0xf3, 0x0, 0x0, 0xdf, 0x30, 0x0, 0xd,
    0xf3, 0x0, 0x0, 0xdf, 0x30, 0x0, 0xd, 0xf3,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0x27, 0x99, 0x99,
    0x91,

    /* U+005C "\\" */
    0x6f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x82,

    /* U+005D "]" */
    0xff, 0xff, 0xff, 0x9a, 0xaa, 0xff, 0x0, 0x1,
    0xff, 0x0, 0x1, 0xff, 0x0, 0x1, 0xff, 0x0,
    0x1, 0xff, 0x0, 0x1, 0xff, 0x0, 0x1, 0xff,
    0x0, 0x1, 0xff, 0x0, 0x1, 0xff, 0x0, 0x1,
    0xff, 0x0, 0x1, 0xff, 0x0, 0x1, 0xff, 0x0,
    0x1, 0xff, 0x0, 0x1, 0xff, 0x0, 0x1, 0xff,
    0x0, 0x1, 0xff, 0x0, 0x1, 0xff, 0x0, 0x1,
    0xff, 0x0, 0x1, 0xff, 0x0, 0x1, 0xff, 0x0,
    0x1, 0xff, 0x0, 0x1, 0xff, 0x0, 0x1, 0xff,
    0x0, 0x1, 0xff, 0x0, 0x1, 0xff, 0x0, 0x1,
    0xff, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0x89,
    0x99, 0x99,

    /* U+005E "^" */
    0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xef, 0x80, 0x0, 0x0, 0x0, 0x4f, 0xe4, 0xfe,
    0x0, 0x0, 0x0, 0xa, 0xf8, 0xe, 0xf4, 0x0,
    0x0, 0x1, 0xff, 0x20, 0x8f, 0xb0, 0x0, 0x0,
    0x6f, 0xd0, 0x2, 0xff, 0x10, 0x0, 0xd, 0xf7,
    0x0, 0xc, 0xf7, 0x0, 0x3, 0xff, 0x10, 0x0,
    0x6f, 0xd0, 0x0, 0x9f, 0xb0, 0x0, 0x1, 0xff,
    0x40, 0xf, 0xf5, 0x0, 0x0, 0xa, 0xfa, 0x6,
    0xfe, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0xcf, 0x90,
    0x0, 0x0, 0x0, 0xef, 0x70,

    /* U+005F "_" */
    0x8d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0x49, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5,

    /* U+0060 "`" */
    0x1, 0x40, 0x0, 0x1, 0xdf, 0x30, 0x0, 0x5f,
    0xfe, 0x10, 0x0, 0x6f, 0xfc, 0x0, 0x0, 0x6f,
    0xfa, 0x0, 0x0, 0x6f, 0xf8, 0x0, 0x0, 0x6f,
    0x80, 0x0, 0x0, 0x30,

    /* U+0061 "a" */
    0x0, 0x0, 0x5a, 0xdf, 0xfc, 0x70, 0x0, 0x0,
    0x6e, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x5, 0xff,
    0xfa, 0x65, 0x7e, 0xff, 0x80, 0x0, 0xb7, 0x0,
    0x0, 0x1, 0xef, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf6, 0x0, 0x0, 0x0, 0x1, 0x47, 0xaf,
    0xf7, 0x0, 0x0, 0x39, 0xef, 0xff, 0xff, 0xf7,
    0x0, 0x2c, 0xff, 0xfb, 0x85, 0x6f, 0xf7, 0x3,
    0xff, 0xe7, 0x0, 0x0, 0x3f, 0xf7, 0xc, 0xfe,
    0x20, 0x0, 0x0, 0x3f, 0xf7, 0x1f, 0xf8, 0x0,
    0x0, 0x0, 0x3f, 0xf7, 0x3f, 0xf7, 0x0, 0x0,
    0x0, 0x5f, 0xf7, 0x1f, 0xfc, 0x0, 0x0, 0x6,
    0xff, 0xf7, 0xc, 0xff, 0xb5, 0x47, 0xcf, 0xdf,
    0xf7, 0x3, 0xff, 0xff, 0xff, 0xfa, 0xe, 0xf7,
    0x0, 0x29, 0xef, 0xea, 0x30, 0xc, 0xf7,

    /* U+0062 "b" */
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x70, 0x29,
    0xdf, 0xeb, 0x40, 0x0, 0x4f, 0xf7, 0x7f, 0xff,
    0xff, 0xff, 0x90, 0x4, 0xff, 0xef, 0xfa, 0x66,
    0xaf, 0xff, 0x70, 0x4f, 0xff, 0xb1, 0x0, 0x0,
    0x4f, 0xff, 0x14, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x9f, 0xf7, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xb4, 0xff, 0x80, 0x0, 0x0, 0x0, 0xf,
    0xfe, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf4, 0xff, 0x80, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xef, 0xe4,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x1f, 0xfc, 0x4f,
    0xf8, 0x0, 0x0, 0x0, 0x6, 0xff, 0x84, 0xff,
    0x80, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x4f, 0xfe,
    0x40, 0x0, 0x0, 0xaf, 0xfa, 0x4, 0xff, 0xff,
    0xc7, 0x68, 0xdf, 0xfe, 0x10, 0x4f, 0xf4, 0xcf,
    0xff, 0xff, 0xfd, 0x10, 0x4, 0xff, 0x0, 0x6c,
    0xff, 0xc7, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x7, 0xce, 0xfe, 0xa3, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xff, 0xff, 0x80, 0x0, 0x4f,
    0xff, 0xd8, 0x68, 0xef, 0xb0, 0x1, 0xef, 0xf7,
    0x0, 0x0, 0x8, 0x10, 0xa, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf5, 0x0, 0x0,
    0x6, 0x70, 0x0, 0x6f, 0xff, 0xc7, 0x57, 0xdf,
    0xf3, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x17, 0xce, 0xfd, 0x92, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xd0, 0x0, 0x2, 0x9e,
    0xfe, 0xb4, 0xd, 0xfd, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xf9, 0xdf, 0xd0, 0x6, 0xff, 0xfc, 0x76,
    0x9e, 0xff, 0xfd, 0x2, 0xff, 0xf6, 0x0, 0x0,
    0x9, 0xff, 0xd0, 0x9f, 0xf8, 0x0, 0x0, 0x0,
    0xe, 0xfd, 0xe, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xd3, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xe,
    0xfd, 0x4f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xd6, 0xff, 0x60, 0x0, 0x0, 0x0, 0xe, 0xfd,
    0x5f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xef, 0xd4,
    0xff, 0x90, 0x0, 0x0, 0x0, 0xe, 0xfd, 0x1f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x1f, 0xfd, 0x6, 0xff,
    0xe2, 0x0, 0x0, 0x3e, 0xff, 0xd0, 0xc, 0xff,
    0xf9, 0x66, 0xbf, 0xfe, 0xfd, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xe4, 0x9f, 0xd0, 0x0, 0x6, 0xbe,
    0xfd, 0x81, 0x7, 0xfd,

    /* U+0065 "e" */
    0x0, 0x0, 0x18, 0xdf, 0xfc, 0x81, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x5f, 0xfe, 0x74, 0x48, 0xff, 0xe1, 0x0, 0x1e,
    0xfc, 0x10, 0x0, 0x2, 0xff, 0x80, 0xa, 0xff,
    0x10, 0x0, 0x0, 0x7, 0xfe, 0x0, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x46, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x46, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x40, 0x0, 0x0, 0x5,
    0x0, 0x0, 0x5f, 0xff, 0xa5, 0x44, 0x7d, 0xf4,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0x0, 0x17, 0xce, 0xfe, 0xb6, 0x0, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x7d, 0xff, 0xd5, 0x0, 0xa, 0xff,
    0xff, 0xf6, 0x0, 0x3f, 0xfe, 0x64, 0x71, 0x0,
    0x9f, 0xf5, 0x0, 0x0, 0x0, 0xbf, 0xf0, 0x0,
    0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0, 0x0, 0xcf,
    0xf0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x22, 0xdf, 0xf2, 0x22, 0x10, 0x0,
    0xcf, 0xf0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0,
    0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0, 0x0, 0xcf,
    0xf0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0,
    0x0, 0xcf, 0xf0, 0x0, 0x0, 0x0, 0xcf, 0xf0,
    0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0, 0x0,
    0xcf, 0xf0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0,
    0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0, 0x0, 0xcf,
    0xf0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0,
    0x0, 0xcf, 0xf0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x6c, 0xef, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0xdf, 0xf8, 0x21, 0x4d, 0xff, 0x52, 0x20,
    0x7, 0xff, 0x60, 0x0, 0x0, 0xdf, 0xc0, 0x0,
    0xb, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0xd, 0xfd, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0xb, 0xfe, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x6, 0xff, 0x40, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0xcf, 0xf6, 0x0, 0x2b, 0xff, 0x40, 0x0,
    0x0, 0x1e, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x7f, 0xca, 0xef, 0xd9, 0x20, 0x0, 0x0,
    0x2, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xc6, 0x44, 0x44, 0x42, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x10,
    0x0, 0x8f, 0xee, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x14, 0xdf, 0xf5,
    0x2f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf7,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf5,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xe0,
    0x1e, 0xff, 0x82, 0x0, 0x2, 0x7e, 0xff, 0x30,
    0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x6, 0xbe, 0xff, 0xec, 0x94, 0x0, 0x0,

    /* U+0068 "h" */
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf7, 0x0, 0x7d, 0xfe, 0xb4, 0x0,
    0x4f, 0xf7, 0x3e, 0xff, 0xff, 0xff, 0x50, 0x4f,
    0xfb, 0xff, 0xb8, 0x8c, 0xff, 0xf1, 0x4f, 0xff,
    0xe4, 0x0, 0x0, 0xbf, 0xf6, 0x4f, 0xfe, 0x20,
    0x0, 0x0, 0x3f, 0xf9, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0xf, 0xfb, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x4f,
    0xf8, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x4f, 0xf8, 0x0,
    0x0, 0x0, 0xf, 0xfc, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0xf, 0xfc, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xfc,

    /* U+0069 "i" */
    0x2d, 0xe6, 0x9f, 0xfe, 0x8f, 0xfd, 0x8, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x4f, 0xf8, 0x4f, 0xf8, 0x4f, 0xf8, 0x4f, 0xf8,
    0x4f, 0xf8, 0x4f, 0xf8, 0x4f, 0xf8, 0x4f, 0xf8,
    0x4f, 0xf8, 0x4f, 0xf8, 0x4f, 0xf8, 0x4f, 0xf8,
    0x4f, 0xf8, 0x4f, 0xf8, 0x4f, 0xf8, 0x4f, 0xf8,

    /* U+006A "j" */
    0x0, 0x0, 0x2d, 0xe6, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x8, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x4f, 0xf7, 0x0, 0x0, 0x8f, 0xf4,
    0x5, 0x46, 0xff, 0xf0, 0xf, 0xff, 0xff, 0x70,
    0x2c, 0xff, 0xd6, 0x0,

    /* U+006B "k" */
    0x4f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x70, 0x0,
    0x0, 0xd, 0xff, 0x30, 0x4f, 0xf7, 0x0, 0x0,
    0xa, 0xff, 0x50, 0x4, 0xff, 0x70, 0x0, 0x7,
    0xff, 0x80, 0x0, 0x4f, 0xf7, 0x0, 0x3, 0xff,
    0xb0, 0x0, 0x4, 0xff, 0x70, 0x1, 0xef, 0xe1,
    0x0, 0x0, 0x4f, 0xf7, 0x0, 0xbf, 0xf3, 0x0,
    0x0, 0x4, 0xff, 0x70, 0x8f, 0xf6, 0x0, 0x0,
    0x0, 0x4f, 0xf7, 0x4f, 0xff, 0x80, 0x0, 0x0,
    0x4, 0xff, 0x9e, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x4f, 0xff, 0xfd, 0x3f, 0xfb, 0x0, 0x0, 0x4,
    0xff, 0xff, 0x20, 0x7f, 0xf5, 0x0, 0x0, 0x4f,
    0xff, 0x40, 0x0, 0xdf, 0xe0, 0x0, 0x4, 0xff,
    0x90, 0x0, 0x5, 0xff, 0x80, 0x0, 0x4f, 0xf7,
    0x0, 0x0, 0xb, 0xff, 0x20, 0x4, 0xff, 0x70,
    0x0, 0x0, 0x2f, 0xfb, 0x0, 0x4f, 0xf7, 0x0,
    0x0, 0x0, 0x9f, 0xf5, 0x4, 0xff, 0x70, 0x0,
    0x0, 0x1, 0xef, 0xe0,

    /* U+006C "l" */
    0x4f, 0xf8, 0x0, 0x4f, 0xf8, 0x0, 0x4f, 0xf8,
    0x0, 0x4f, 0xf8, 0x0, 0x4f, 0xf8, 0x0, 0x4f,
    0xf8, 0x0, 0x4f, 0xf8, 0x0, 0x4f, 0xf8, 0x0,
    0x4f, 0xf8, 0x0, 0x4f, 0xf8, 0x0, 0x4f, 0xf8,
    0x0, 0x4f, 0xf8, 0x0, 0x4f, 0xf8, 0x0, 0x4f,
    0xf8, 0x0, 0x4f, 0xf8, 0x0, 0x4f, 0xf8, 0x0,
    0x4f, 0xf8, 0x0, 0x4f, 0xf8, 0x0, 0x4f, 0xf8,
    0x0, 0x4f, 0xf8, 0x0, 0x3f, 0xf8, 0x0, 0x2f,
    0xfd, 0x40, 0xd, 0xff, 0xf0, 0x2, 0xcf, 0xe2,

    /* U+006D "m" */
    0x4f, 0xf0, 0x1, 0x8d, 0xfd, 0x91, 0x0, 0x1,
    0x9d, 0xfd, 0x80, 0x0, 0x4f, 0xf2, 0x4e, 0xff,
    0xff, 0xfd, 0x10, 0x4f, 0xff, 0xff, 0xfc, 0x0,
    0x4f, 0xf8, 0xff, 0xb7, 0x9f, 0xff, 0x96, 0xff,
    0xa7, 0xaf, 0xff, 0x70, 0x4f, 0xff, 0xe3, 0x0,
    0x3, 0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xd0,
    0x4f, 0xfd, 0x10, 0x0, 0x0, 0xaf, 0xfc, 0x0,
    0x0, 0x0, 0xcf, 0xf0, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0x7f, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xf2,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x0, 0x8f, 0xf3, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x0, 0x8f, 0xf3, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x0, 0x8f, 0xf3, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x0, 0x8f, 0xf3, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x0, 0x8f, 0xf3, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x0, 0x8f, 0xf3,

    /* U+006E "n" */
    0x4f, 0xf0, 0x0, 0x7d, 0xfe, 0xb4, 0x0, 0x4f,
    0xf2, 0x3d, 0xff, 0xff, 0xff, 0x50, 0x4f, 0xf8,
    0xff, 0xb8, 0x8c, 0xff, 0xf1, 0x4f, 0xff, 0xe4,
    0x0, 0x0, 0xbf, 0xf6, 0x4f, 0xfe, 0x20, 0x0,
    0x0, 0x3f, 0xf9, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0xf, 0xfb, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x4f,
    0xf8, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x4f, 0xf8, 0x0,
    0x0, 0x0, 0xf, 0xfc, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0xf, 0xfc, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0xf, 0xfc, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xfc,

    /* U+006F "o" */
    0x0, 0x0, 0x18, 0xcf, 0xfd, 0x92, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x5f, 0xff, 0xa6, 0x6a, 0xff, 0xf8, 0x0,
    0x2, 0xff, 0xf4, 0x0, 0x0, 0x2e, 0xff, 0x40,
    0xa, 0xff, 0x60, 0x0, 0x0, 0x3, 0xff, 0xd0,
    0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf3,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf6,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9,
    0x7f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf9,
    0x6f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf9,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x1f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf3,
    0xa, 0xff, 0x50, 0x0, 0x0, 0x3, 0xff, 0xd0,
    0x2, 0xff, 0xf3, 0x0, 0x0, 0x2d, 0xff, 0x40,
    0x0, 0x5f, 0xff, 0xa6, 0x59, 0xff, 0xf8, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x17, 0xce, 0xfd, 0x82, 0x0, 0x0,

    /* U+0070 "p" */
    0x4f, 0xf1, 0x2, 0x9d, 0xfe, 0xb4, 0x0, 0x4,
    0xff, 0x26, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x4f,
    0xfd, 0xff, 0xa6, 0x6a, 0xff, 0xf7, 0x4, 0xff,
    0xfb, 0x10, 0x0, 0x4, 0xff, 0xf1, 0x4f, 0xfa,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x74, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x3f, 0xfb, 0x4f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xe4, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf4, 0xff, 0x80, 0x0, 0x0,
    0x0, 0xe, 0xfe, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xc4, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x6f, 0xf8, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x24, 0xff, 0xe4, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0x4f, 0xff, 0xfc, 0x76, 0x8d, 0xff, 0xe1,
    0x4, 0xff, 0x9d, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x4f, 0xf7, 0x6, 0xcf, 0xfc, 0x70, 0x0, 0x4,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x29, 0xef, 0xeb, 0x40, 0x6f, 0xd0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xba, 0xfd, 0x0,
    0x6f, 0xff, 0xc7, 0x69, 0xef, 0xff, 0xd0, 0x2f,
    0xff, 0x60, 0x0, 0x0, 0x9f, 0xfd, 0x9, 0xff,
    0x80, 0x0, 0x0, 0x0, 0xef, 0xd0, 0xef, 0xf0,
    0x0, 0x0, 0x0, 0xe, 0xfd, 0x3f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xd4, 0xff, 0x70, 0x0,
    0x0, 0x0, 0xe, 0xfd, 0x6f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xd5, 0xff, 0x70, 0x0, 0x0,
    0x0, 0xe, 0xfd, 0x4f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xd1, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0xe, 0xfd, 0xc, 0xff, 0x40, 0x0, 0x0, 0x1,
    0xff, 0xd0, 0x6f, 0xfe, 0x20, 0x0, 0x3, 0xef,
    0xfd, 0x0, 0xcf, 0xff, 0x96, 0x6b, 0xff, 0xff,
    0xd0, 0x1, 0xcf, 0xff, 0xff, 0xfe, 0x4c, 0xfd,
    0x0, 0x0, 0x6b, 0xef, 0xd8, 0x10, 0xdf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfd,

    /* U+0072 "r" */
    0x4f, 0xf0, 0x2, 0xae, 0xf9, 0x4f, 0xf2, 0x3f,
    0xff, 0xf6, 0x4f, 0xf4, 0xef, 0xc8, 0x93, 0x4f,
    0xfe, 0xf6, 0x0, 0x0, 0x4f, 0xff, 0x70, 0x0,
    0x0, 0x4f, 0xfd, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0,
    0x0, 0x0, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0x4f,
    0xf8, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0, 0x0,
    0x0, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0x4f, 0xf8, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x0, 0x6c, 0xef, 0xea, 0x50, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x9f, 0xfb,
    0x43, 0x4a, 0xfe, 0x10, 0xf, 0xfc, 0x0, 0x0,
    0x3, 0x30, 0x1, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfc, 0x40, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xd7, 0x10, 0x0, 0x0, 0x0, 0x5c, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x3, 0x9f, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xe0, 0x2d, 0x40, 0x0,
    0x0, 0x1f, 0xfb, 0xc, 0xff, 0xb6, 0x33, 0x6d,
    0xff, 0x40, 0x2c, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x4, 0x9d, 0xff, 0xd9, 0x20, 0x0,

    /* U+0074 "t" */
    0x0, 0x2, 0x87, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x60, 0x22,
    0xcf, 0xf2, 0x22, 0x21, 0x0, 0xc, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x0, 0x0, 0x0, 0x8, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x74, 0x64, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x8d, 0xfe,
    0xc7,

    /* U+0075 "u" */
    0x7f, 0xf4, 0x0, 0x0, 0x0, 0x4f, 0xf7, 0x7f,
    0xf4, 0x0, 0x0, 0x0, 0x4f, 0xf7, 0x7f, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xf7, 0x7f, 0xf4, 0x0,
    0x0, 0x0, 0x4f, 0xf7, 0x7f, 0xf4, 0x0, 0x0,
    0x0, 0x4f, 0xf7, 0x7f, 0xf4, 0x0, 0x0, 0x0,
    0x4f, 0xf7, 0x7f, 0xf4, 0x0, 0x0, 0x0, 0x4f,
    0xf7, 0x7f, 0xf4, 0x0, 0x0, 0x0, 0x4f, 0xf7,
    0x7f, 0xf4, 0x0, 0x0, 0x0, 0x4f, 0xf7, 0x7f,
    0xf4, 0x0, 0x0, 0x0, 0x4f, 0xf7, 0x7f, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xf7, 0x7f, 0xf5, 0x0,
    0x0, 0x0, 0x4f, 0xf7, 0x5f, 0xf8, 0x0, 0x0,
    0x0, 0xdf, 0xf7, 0x1f, 0xfe, 0x10, 0x0, 0x1c,
    0xff, 0xf7, 0xc, 0xff, 0xe8, 0x79, 0xff, 0x6f,
    0xf7, 0x2, 0xff, 0xff, 0xff, 0xf6, 0xe, 0xf7,
    0x0, 0x2a, 0xef, 0xe9, 0x20, 0xd, 0xf7,

    /* U+0076 "v" */
    0x7f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf1,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xef, 0xb0,
    0xb, 0xff, 0x0, 0x0, 0x0, 0x4, 0xff, 0x60,
    0x6, 0xff, 0x50, 0x0, 0x0, 0x9, 0xff, 0x10,
    0x1, 0xff, 0xa0, 0x0, 0x0, 0xe, 0xfb, 0x0,
    0x0, 0xbf, 0xf0, 0x0, 0x0, 0x3f, 0xf5, 0x0,
    0x0, 0x5f, 0xf4, 0x0, 0x0, 0x8f, 0xf0, 0x0,
    0x0, 0xf, 0xf9, 0x0, 0x0, 0xdf, 0xb0, 0x0,
    0x0, 0xa, 0xfe, 0x0, 0x2, 0xff, 0x50, 0x0,
    0x0, 0x5, 0xff, 0x30, 0x7, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x80, 0xc, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xd0, 0x1f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf2, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf7, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfc, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xfa, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x5f, 0xf9, 0x0,
    0x0, 0x0, 0x7f, 0xf2, 0xc, 0xff, 0x0, 0x0,
    0x0, 0xaf, 0xfd, 0x0, 0x0, 0x0, 0xbf, 0xe0,
    0x8, 0xff, 0x30, 0x0, 0x0, 0xef, 0xff, 0x10,
    0x0, 0x0, 0xff, 0xa0, 0x4, 0xff, 0x70, 0x0,
    0x2, 0xfd, 0xcf, 0x50, 0x0, 0x3, 0xff, 0x50,
    0x0, 0xff, 0xb0, 0x0, 0x6, 0xf9, 0x9f, 0x90,
    0x0, 0x7, 0xff, 0x10, 0x0, 0xbf, 0xf0, 0x0,
    0xa, 0xf6, 0x5f, 0xe0, 0x0, 0xb, 0xfd, 0x0,
    0x0, 0x7f, 0xf3, 0x0, 0xe, 0xf2, 0x1f, 0xf2,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0x3f, 0xf7, 0x0,
    0x2f, 0xe0, 0xe, 0xf6, 0x0, 0x2f, 0xf5, 0x0,
    0x0, 0xe, 0xfb, 0x0, 0x7f, 0xa0, 0xa, 0xfa,
    0x0, 0x6f, 0xf1, 0x0, 0x0, 0xa, 0xff, 0x0,
    0xbf, 0x60, 0x5, 0xfe, 0x0, 0xaf, 0xd0, 0x0,
    0x0, 0x6, 0xff, 0x20, 0xef, 0x20, 0x1, 0xff,
    0x20, 0xef, 0x80, 0x0, 0x0, 0x1, 0xff, 0x52,
    0xfe, 0x0, 0x0, 0xdf, 0x61, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xdf, 0x86, 0xfa, 0x0, 0x0, 0x9f,
    0xa4, 0xff, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xca,
    0xf6, 0x0, 0x0, 0x5f, 0xd8, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xfe, 0xf2, 0x0, 0x0, 0x1f,
    0xfd, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xe0, 0x0, 0x0, 0xd, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xa0, 0x0, 0x0, 0x9,
    0xff, 0xf0, 0x0, 0x0,

    /* U+0078 "x" */
    0xd, 0xff, 0x20, 0x0, 0x0, 0x1f, 0xfc, 0x0,
    0x3f, 0xfb, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0,
    0x9f, 0xf4, 0x0, 0x2, 0xff, 0x90, 0x0, 0x1,
    0xef, 0xd0, 0x0, 0xaf, 0xe1, 0x0, 0x0, 0x6,
    0xff, 0x70, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0xc,
    0xfe, 0x1b, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfb, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xd6, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x3f, 0xf5, 0xc, 0xff,
    0x10, 0x0, 0x0, 0xc, 0xfc, 0x0, 0x3f, 0xfa,
    0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x9f, 0xf4,
    0x0, 0x1, 0xff, 0xb0, 0x0, 0x1, 0xef, 0xe0,
    0x0, 0xaf, 0xf2, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x3f, 0xf9, 0x0, 0x0, 0x0, 0xc, 0xff, 0x20,

    /* U+0079 "y" */
    0x6f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf1,
    0x1f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xef, 0xb0,
    0xa, 0xff, 0x10, 0x0, 0x0, 0x3, 0xff, 0x60,
    0x4, 0xff, 0x70, 0x0, 0x0, 0x8, 0xff, 0x10,
    0x0, 0xdf, 0xd0, 0x0, 0x0, 0xd, 0xfb, 0x0,
    0x0, 0x7f, 0xf2, 0x0, 0x0, 0x2f, 0xf6, 0x0,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0x0, 0xb, 0xfe, 0x0, 0x0, 0xcf, 0xb0, 0x0,
    0x0, 0x5, 0xff, 0x30, 0x1, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xef, 0x90, 0x6, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x8f, 0xe0, 0xb, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf4, 0xf, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf9, 0x4f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfe, 0x8f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x3, 0x55, 0xcf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x4, 0x44, 0x44,
    0x44, 0xbf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x90, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfc, 0x44, 0x44, 0x44,
    0x44, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,

    /* U+007B "{" */
    0x0, 0x0, 0x9e, 0xff, 0x20, 0x0, 0xaf, 0xfc,
    0xa1, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x3, 0xff,
    0x10, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0, 0x4,
    0xff, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x2, 0xff, 0x0, 0x0, 0x0, 0x1f, 0xf1, 0x0,
    0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0xf, 0xf2,
    0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x3d, 0xfb, 0x0, 0x0, 0xdf,
    0xfa, 0x10, 0x0, 0xa, 0xff, 0xd3, 0x0, 0x0,
    0x0, 0xbf, 0xc0, 0x0, 0x0, 0x2, 0xff, 0x0,
    0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0xff,
    0x20, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0x2,
    0xff, 0x10, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x3, 0xff, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0,
    0x0, 0x4, 0xff, 0x0, 0x0, 0x0, 0x2f, 0xf2,
    0x0, 0x0, 0x0, 0xef, 0xb2, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x20, 0x0, 0x2, 0x79, 0x91,

    /* U+007C "|" */
    0xef, 0xe, 0xf0, 0xef, 0xe, 0xf0, 0xef, 0xe,
    0xf0, 0xef, 0xe, 0xf0, 0xef, 0xe, 0xf0, 0xef,
    0xe, 0xf0, 0xef, 0xe, 0xf0, 0xef, 0xe, 0xf0,
    0xef, 0xe, 0xf0, 0xef, 0xe, 0xf0, 0xef, 0xe,
    0xf0, 0xef, 0xe, 0xf0, 0xef, 0xe, 0xf0, 0xef,
    0xe, 0xf0, 0xef, 0xe, 0xf0, 0xef, 0xe, 0xf0,
    0xef, 0xe, 0xf0,

    /* U+007D "}" */
    0xff, 0xea, 0x10, 0x0, 0x9b, 0xff, 0xc0, 0x0,
    0x0, 0x4f, 0xf3, 0x0, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0xd, 0xf7, 0x0, 0x0, 0xd, 0xf6, 0x0,
    0x0, 0xe, 0xf6, 0x0, 0x0, 0xe, 0xf5, 0x0,
    0x0, 0xe, 0xf4, 0x0, 0x0, 0xf, 0xf3, 0x0,
    0x0, 0xf, 0xf3, 0x0, 0x0, 0xf, 0xf3, 0x0,
    0x0, 0xd, 0xf6, 0x0, 0x0, 0x8, 0xfe, 0x40,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x1, 0xcf, 0xfc,
    0x0, 0xa, 0xfd, 0x10, 0x0, 0xe, 0xf5, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0x0, 0xf, 0xf3, 0x0,
    0x0, 0xf, 0xf3, 0x0, 0x0, 0xe, 0xf4, 0x0,
    0x0, 0xe, 0xf5, 0x0, 0x0, 0xd, 0xf6, 0x0,
    0x0, 0xd, 0xf7, 0x0, 0x0, 0xe, 0xf6, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x1, 0x9f, 0xf1, 0x0,
    0xff, 0xff, 0x80, 0x0, 0x89, 0x73, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x6d, 0xfc, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xa0, 0x0, 0x8, 0xb1, 0x5f,
    0xe5, 0x27, 0xff, 0xd4, 0x27, 0xff, 0x24, 0xe3,
    0x0, 0x3, 0xdf, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x8e, 0xfb, 0x30, 0x0,

    /* U+4EAE "亮" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x2d, 0xfb, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x2c, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0xaf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xa, 0xfe, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xef, 0x90, 0xa, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x90, 0xa, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x90, 0x9, 0xf7, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x9f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xee, 0xee, 0xee,
    0xef, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0x8, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x0, 0x0, 0x0, 0x8, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x0, 0x8, 0xfd, 0x0, 0x0, 0xd, 0x90,
    0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0, 0x0, 0x0,
    0x8, 0xfd, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x1,
    0x6d, 0xff, 0x70, 0x0, 0x0, 0x0, 0x8, 0xfe,
    0x0, 0x0, 0x4f, 0xe0, 0x8, 0xcf, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x9, 0xff, 0xd7, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xae, 0xff, 0xff, 0xfc, 0x10,
    0x1, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5173 "关" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8e, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xfd, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x7f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0x80, 0x0, 0x0, 0x1, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8a, 0x20, 0x0, 0x0, 0xa, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x5,
    0x55, 0x55, 0x55, 0x55, 0x6f, 0xf9, 0x55, 0x55,
    0x55, 0x55, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x6f, 0xf7, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x30, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x3f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf8, 0x7,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xc0, 0x0, 0xbf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfe, 0x20, 0x0, 0xd, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xd2,
    0x0, 0x0, 0x1, 0xdf, 0xfb, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x3b, 0xff, 0xfb, 0x10, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xf8, 0x10, 0x0, 0x0, 0x5c,
    0xff, 0xfe, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xfa, 0x51, 0x3f, 0xff, 0xff, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf,
    0xff, 0xf6, 0x8, 0xfd, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xaf, 0xa0,
    0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+51FA "出" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x65, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0x67, 0x30, 0x8, 0xfc, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0xef, 0x80,
    0x8, 0xfc, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0xef, 0x80, 0x8, 0xfc, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0xef, 0x80,
    0x8, 0xfc, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0xef, 0x80, 0x8, 0xfc, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0xef, 0x80,
    0x8, 0xfc, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0xef, 0x80, 0x8, 0xfc, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0xef, 0x80,
    0x8, 0xfd, 0x44, 0x44, 0x44, 0x7f, 0xf5, 0x44,
    0x44, 0x44, 0xff, 0x80, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x89, 0x50, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0x6, 0x96, 0xef, 0x90, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0xb, 0xfb,
    0xef, 0x90, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0xb, 0xfb, 0xef, 0x90, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0xb, 0xfb,
    0xef, 0x90, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0xb, 0xfb, 0xef, 0x90, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0xb, 0xfb,
    0xef, 0x90, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0xb, 0xfb, 0xef, 0x90, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0xb, 0xfb,
    0xef, 0x90, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0xb, 0xfb, 0xef, 0xb6, 0x66, 0x66,
    0x66, 0x9f, 0xf7, 0x66, 0x66, 0x66, 0x6d, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xcd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfb,

    /* U+522B "别" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x50, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf5, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x50, 0x2f, 0xe2, 0x22, 0x22, 0x22, 0x5f, 0xe0,
    0x0, 0x19, 0x90, 0x0, 0xe, 0xf5, 0x2, 0xfe,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x2, 0xff,
    0x0, 0x0, 0xef, 0x50, 0x2f, 0xe0, 0x0, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x2f, 0xf0, 0x0, 0xe,
    0xf5, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x2, 0xff, 0x0, 0x0, 0xef, 0x50, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x2f,
    0xf0, 0x0, 0xe, 0xf5, 0x2, 0xfe, 0x11, 0x11,
    0x11, 0x14, 0xfe, 0x0, 0x2, 0xff, 0x0, 0x0,
    0xef, 0x50, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x2f, 0xf0, 0x0, 0xe, 0xf5, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x2,
    0xff, 0x0, 0x0, 0xef, 0x50, 0x0, 0x0, 0x3,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0, 0x0,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0xef, 0x30, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x0, 0x0, 0xef, 0x50,
    0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf0, 0x0, 0xe, 0xf5, 0x3a, 0xaa, 0xaa,
    0xff, 0xaa, 0xaa, 0xaa, 0x40, 0x2, 0xff, 0x0,
    0x0, 0xef, 0x55, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x2f, 0xf0, 0x0, 0xe, 0xf5,
    0x16, 0x66, 0x69, 0xfe, 0x66, 0x66, 0xef, 0x50,
    0x2, 0xff, 0x0, 0x0, 0xef, 0x50, 0x0, 0x0,
    0x7f, 0xa0, 0x0, 0xe, 0xf4, 0x0, 0x2f, 0xf0,
    0x0, 0xe, 0xf5, 0x0, 0x0, 0xa, 0xf7, 0x0,
    0x0, 0xff, 0x30, 0x2, 0xff, 0x0, 0x0, 0xef,
    0x50, 0x0, 0x0, 0xff, 0x30, 0x0, 0xf, 0xf2,
    0x0, 0x2f, 0xf0, 0x0, 0xe, 0xf5, 0x0, 0x0,
    0x5f, 0xe0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x50, 0x0, 0xc, 0xf8, 0x0,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf5, 0x0, 0x7, 0xff, 0x20, 0x0, 0x5, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x50, 0x4,
    0xff, 0x70, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf5, 0x7, 0xff, 0xb0, 0x4,
    0x54, 0x5e, 0xf8, 0x0, 0x0, 0x2, 0x22, 0x23,
    0xff, 0x4a, 0xff, 0xb0, 0x0, 0x8f, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf1, 0x1e,
    0x70, 0x0, 0x3, 0xcc, 0xc9, 0x40, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xc5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0,

    /* U+529F "功" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xee, 0xee, 0xee, 0xee, 0xeb,
    0x0, 0x0, 0xe, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x14, 0x44,
    0x4d, 0xf9, 0x44, 0x43, 0x0, 0x0, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x33, 0x33, 0x3f, 0xf6, 0x33, 0x33,
    0x33, 0x31, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1,
    0x0, 0x0, 0xd, 0xf5, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0xe, 0xf4, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf0, 0x0, 0x0, 0xf, 0xf4,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xd0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xa0,
    0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x80, 0x0, 0x0,
    0x1f, 0xf1, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x2f, 0xf0,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x15, 0x20, 0x5,
    0xff, 0x10, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0xc, 0xf9, 0x8c, 0xff, 0x60, 0xa, 0xfc, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x3, 0x7e, 0xff,
    0xff, 0xfd, 0x50, 0xf, 0xf6, 0x0, 0x0, 0x0,
    0x6f, 0xd0, 0x5b, 0xff, 0xff, 0xff, 0xb6, 0x20,
    0x0, 0x8f, 0xf1, 0x0, 0x0, 0x0, 0x7f, 0xc0,
    0x6f, 0xff, 0xd8, 0x40, 0x0, 0x0, 0x2, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x9f, 0xa0, 0x2a, 0x51,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x10, 0x0,
    0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xfb, 0x0,
    0x0, 0x32, 0x21, 0x2b, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xa0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf6, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfd,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5382 "厂" */
    0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0xdf, 0xa4, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0x0, 0xd,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+53D6 "取" */
    0x8e, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x9f, 0xa2, 0x22,
    0x26, 0xfe, 0x22, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x8f, 0x90, 0x0, 0x5, 0xfe,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x8f, 0x90, 0x0, 0x5, 0xfe, 0x0, 0x26,
    0xfc, 0x33, 0x33, 0x33, 0xaf, 0x80, 0x0, 0x8f,
    0x90, 0x0, 0x5, 0xfe, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0x8f, 0xec, 0xcc,
    0xcd, 0xfe, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0xef, 0x30, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0xbf, 0x40, 0x0, 0x2, 0xff, 0x0,
    0x0, 0x8f, 0xa3, 0x33, 0x37, 0xfe, 0x0, 0x0,
    0x8f, 0x80, 0x0, 0x6, 0xfc, 0x0, 0x0, 0x8f,
    0x90, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x4f, 0xb0,
    0x0, 0xa, 0xf7, 0x0, 0x0, 0x8f, 0x90, 0x0,
    0x5, 0xfe, 0x0, 0x0, 0xf, 0xf0, 0x0, 0xe,
    0xf3, 0x0, 0x0, 0x8f, 0x90, 0x0, 0x5, 0xfe,
    0x0, 0x0, 0xc, 0xf4, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x8f, 0xa0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x7, 0xf9, 0x0, 0xaf, 0x90, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x2, 0xfe,
    0x0, 0xff, 0x30, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0xdf, 0x46, 0xfd,
    0x0, 0x0, 0x0, 0x8f, 0x90, 0x0, 0x5, 0xfe,
    0x0, 0x0, 0x0, 0x8f, 0xbd, 0xf6, 0x0, 0x0,
    0x0, 0x8f, 0x90, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8f,
    0x90, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x8f, 0x90, 0x0,
    0x28, 0xff, 0xce, 0x70, 0x0, 0x9, 0xff, 0x40,
    0x0, 0x0, 0x2, 0xaf, 0xdb, 0xdf, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x5f, 0xff, 0xe0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xdc, 0xfe, 0x20, 0x0,
    0x2, 0xff, 0xae, 0xfb, 0x0, 0x0, 0x8f, 0xca,
    0x75, 0x20, 0x5, 0xfe, 0x0, 0x0, 0x1e, 0xfc,
    0x4, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x0, 0x2, 0xef, 0xd1, 0x0, 0x7f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfe,
    0x0, 0x5f, 0xfe, 0x20, 0x0, 0x8, 0xff, 0xc2,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x5, 0xff,
    0xc1, 0x0, 0x0, 0x0, 0x6f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfe, 0x0, 0x99, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+53F7 "号" */
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf7, 0x0,
    0x0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x70, 0x0, 0x0, 0x0,
    0x7f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf7, 0x0, 0x0, 0x0, 0x7, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x70,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x2, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x21, 0x0, 0x2,
    0xdf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+540C "同" */
    0x3d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xd4, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x4f, 0xf4, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x4f, 0xf4, 0x4f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf4, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf4, 0x4f, 0xe0, 0x0, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x20, 0xe, 0xf4, 0x4f, 0xe0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xe, 0xf4, 0x4f, 0xe0, 0x4, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xb0, 0xe, 0xf4,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf4, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf4, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x4f,
    0xe0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xe, 0xf4, 0x4f, 0xe0, 0x0, 0xc,
    0xfe, 0xdd, 0xdd, 0xdd, 0xdf, 0xf5, 0x0, 0xe,
    0xf4, 0x4f, 0xe0, 0x0, 0xc, 0xf4, 0x0, 0x0,
    0x0, 0xc, 0xf5, 0x0, 0xe, 0xf4, 0x4f, 0xe0,
    0x0, 0xc, 0xf4, 0x0, 0x0, 0x0, 0xc, 0xf5,
    0x0, 0xe, 0xf4, 0x4f, 0xe0, 0x0, 0xc, 0xf4,
    0x0, 0x0, 0x0, 0xc, 0xf5, 0x0, 0xe, 0xf4,
    0x4f, 0xe0, 0x0, 0xc, 0xf4, 0x0, 0x0, 0x0,
    0xc, 0xf5, 0x0, 0xe, 0xf4, 0x4f, 0xe0, 0x0,
    0xc, 0xf4, 0x0, 0x0, 0x0, 0xc, 0xf5, 0x0,
    0xe, 0xf4, 0x4f, 0xe0, 0x0, 0xc, 0xfe, 0xdd,
    0xdd, 0xdd, 0xdf, 0xf5, 0x0, 0xe, 0xf4, 0x4f,
    0xe0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xe, 0xf4, 0x4f, 0xe0, 0x0, 0xc,
    0xf5, 0x11, 0x11, 0x11, 0x11, 0x10, 0x0, 0xe,
    0xf4, 0x4f, 0xe0, 0x0, 0xc, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x4f, 0xe0,
    0x0, 0x8, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf4, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x2f, 0xf4,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xf1, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xfd, 0x50, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,

    /* U+578B "型" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf9, 0x0, 0x2, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xd4, 0x0, 0x44, 0x0, 0x9,
    0xf9, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x2, 0xfe, 0x0, 0x9, 0xf9, 0x0,
    0x0, 0x33, 0x8f, 0xc3, 0x33, 0xaf, 0xa3, 0x31,
    0x2, 0xfe, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x0,
    0x6f, 0xb0, 0x0, 0x9f, 0x80, 0x0, 0x2, 0xfe,
    0x0, 0x9, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xb0,
    0x0, 0x9f, 0x80, 0x0, 0x2, 0xfe, 0x0, 0x9,
    0xf9, 0x0, 0x0, 0x0, 0x6f, 0xb0, 0x0, 0x9f,
    0x80, 0x0, 0x2, 0xfe, 0x0, 0x9, 0xf9, 0x0,
    0x2a, 0xaa, 0xcf, 0xea, 0xaa, 0xdf, 0xda, 0xaa,
    0x2, 0xfe, 0x0, 0x9, 0xf9, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2, 0xfe,
    0x0, 0x9, 0xf9, 0x0, 0x16, 0x66, 0xcf, 0xa6,
    0x66, 0xbf, 0xb6, 0x65, 0x2, 0xfe, 0x0, 0x9,
    0xf9, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x9f,
    0x80, 0x0, 0x2, 0xfe, 0x0, 0x9, 0xf9, 0x0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0x9f, 0x80, 0x0,
    0x2, 0xcb, 0x0, 0x9, 0xf9, 0x0, 0x0, 0xd,
    0xf9, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf9, 0x0, 0x1, 0xcf, 0xf1, 0x0,
    0x0, 0x9f, 0x80, 0x0, 0x0, 0x1, 0x10, 0x1a,
    0xf9, 0x0, 0x2e, 0xff, 0x40, 0x0, 0x0, 0x9f,
    0x92, 0x20, 0x0, 0x8, 0xff, 0xff, 0xf6, 0x0,
    0xa, 0xe4, 0x0, 0x0, 0x0, 0x59, 0x8f, 0xf1,
    0x0, 0x3, 0xff, 0xec, 0x80, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xdd, 0xdd, 0xdd, 0xdd,
    0xef, 0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0x30, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x2,
    0x33, 0x33, 0x33, 0x33, 0x6f, 0xf4, 0x33, 0x33,
    0x33, 0x33, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x12, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,

    /* U+590D "复" */
    0x0, 0x0, 0x0, 0x1d, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x51, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x3, 0xef, 0xed, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0x30,
    0x0, 0x4, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xd5, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x10, 0x0, 0x1, 0xef, 0xc1,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x3, 0x90, 0x6, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf9, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0xaf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x39, 0x9b, 0xff, 0xb9,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xba, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x2b, 0xff, 0x93, 0xef, 0x90,
    0x0, 0x0, 0x1, 0xaf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xfe, 0x50, 0x1, 0xdf, 0xe6, 0x0,
    0x17, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x10, 0x0, 0x0, 0x7f, 0xfe, 0xaf, 0xff,
    0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xbf, 0xff, 0xff, 0xa3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x7a,
    0xdf, 0xff, 0xfd, 0x9d, 0xff, 0xff, 0xc9, 0x75,
    0x32, 0x0, 0x7, 0xce, 0xff, 0xff, 0xff, 0xfb,
    0x72, 0x0, 0x3, 0x8c, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x3f, 0xff, 0xec, 0x96, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x36, 0x9b, 0xdf, 0xf2, 0x0,
    0x42, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5C0F "小" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x22,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x50, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfd, 0x50, 0x0, 0x1, 0xff,
    0x50, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x40, 0x0, 0x1, 0xff, 0x50,
    0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0,
    0x0, 0xbf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xfc, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0, 0x0,
    0x3f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf7,
    0x0, 0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0xb,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf2, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0x3, 0xff,
    0x70, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0,
    0x1, 0xff, 0x50, 0x0, 0x0, 0x0, 0xcf, 0xe0,
    0x0, 0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x1,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0x0,
    0x0, 0xa, 0xff, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x50, 0x0, 0x0, 0x0, 0xe, 0xfc, 0x0, 0x0,
    0x3f, 0xf8, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x20, 0x0, 0xbf,
    0xf1, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x80, 0x7, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xd0, 0x8, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf2, 0x0, 0x22, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x4c, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x34, 0x33, 0x36, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xfe, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x33, 0x33, 0x33, 0x33,
    0x33, 0xbf, 0xd3, 0x33, 0x33, 0x33, 0x33, 0x31,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x3f, 0xf0, 0x0,
    0x0, 0x16, 0x50, 0x0, 0x0, 0x0, 0x66, 0x10,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x3f,
    0xe0, 0x0, 0x0, 0x0, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0x3f, 0xe0, 0x0,
    0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x3f,
    0xf3, 0xdd, 0xdd, 0xdf, 0xfd, 0xdd, 0xdd, 0xdd,
    0xff, 0xdd, 0xdd, 0xd1, 0x0, 0x3f, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x4f,
    0xe0, 0x0, 0x0, 0x0, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0x3f, 0xe0, 0x0,
    0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0,
    0x0, 0x3f, 0xfc, 0xcc, 0xcc, 0xcc, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x9f, 0x90, 0xbe, 0xee, 0xff,
    0xee, 0xee, 0xee, 0xee, 0xef, 0xff, 0x70, 0x0,
    0x0, 0xcf, 0x70, 0x0, 0x7, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0xc, 0xfc, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0xcf, 0xe2, 0x0, 0x0, 0x2,
    0xdf, 0xd1, 0x0, 0x0, 0x2, 0xff, 0x10, 0x0,
    0x0, 0xb, 0xff, 0x70, 0x0, 0x7f, 0xfc, 0x10,
    0x0, 0x0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xfd, 0x7e, 0xff, 0x90, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5,
    0x0, 0x0, 0x25, 0x8c, 0xff, 0xff, 0xef, 0xff,
    0xd9, 0x52, 0x0, 0x0, 0x7f, 0xf0, 0xc, 0xef,
    0xff, 0xff, 0xfb, 0x71, 0x3, 0x9e, 0xff, 0xff,
    0xfe, 0xb6, 0x7f, 0x80, 0x9, 0xff, 0xc9, 0x74,
    0x0, 0x0, 0x0, 0x0, 0x37, 0xbe, 0xff, 0xf1,
    0x1, 0x10, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x30,

    /* U+5F85 "待" */
    0x0, 0x0, 0x0, 0x6a, 0x40, 0x0, 0x0, 0x0,
    0x8, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xfd, 0x10,
    0x0, 0x11, 0x11, 0x11, 0xaf, 0xa1, 0x11, 0x11,
    0x10, 0x0, 0x0, 0x3e, 0xfd, 0x10, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x7f, 0xfd, 0x10, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x3f,
    0xfb, 0x10, 0x2, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa8, 0x0,
    0x2, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x9f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xb0, 0x0, 0x0, 0x0, 0x9, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf2, 0x33,
    0x33, 0x33, 0x33, 0xbf, 0xb3, 0x33, 0x33, 0x33,
    0x30, 0x0, 0x0, 0x6f, 0xf6, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x5f, 0xfe, 0x0, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xef, 0xfd, 0xdd, 0xd1, 0x0, 0x5f,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x6f, 0xfe, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xd0, 0x0, 0x0, 0x9f, 0xfd, 0x5f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfd, 0x0,
    0x0, 0x6, 0xfc, 0x13, 0xfe, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x8, 0x0, 0x3f, 0xe0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x3, 0xfe, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x6f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xe0, 0x0, 0x1, 0x9e, 0x20, 0x0, 0x0, 0x6,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0,
    0x0, 0xc, 0xfd, 0x0, 0x0, 0x0, 0x6f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0,
    0x1e, 0xfa, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xfe, 0x0, 0x0, 0x0, 0x4f,
    0xf5, 0x0, 0x0, 0x6f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0, 0x9f, 0xe0,
    0x0, 0x6, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfe, 0x0, 0x0, 0x0, 0x1, 0xc3, 0x0, 0x0,
    0x6f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x18, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xfe, 0xb2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6062 "恢" */
    0x0, 0x1, 0x98, 0x0, 0x0, 0x0, 0x1, 0x97,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0,
    0x6, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x8, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xfe, 0x25, 0xb, 0xdd, 0xdf, 0xfe, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xd1, 0x5, 0x83, 0xfe, 0xbf,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x9, 0xf4, 0xfe, 0x5f, 0x82, 0x33,
    0x3f, 0xf4, 0x33, 0x33, 0x33, 0x33, 0x33, 0x30,
    0xa, 0xf3, 0xfe, 0xf, 0xe0, 0x0, 0x2f, 0xe0,
    0x0, 0x1, 0xb8, 0x0, 0x0, 0x0, 0xd, 0xf2,
    0xfe, 0xa, 0xf3, 0x0, 0x5f, 0xb0, 0x0, 0x2,
    0xfb, 0x0, 0x0, 0x0, 0xf, 0xd2, 0xfe, 0x5,
    0xf7, 0x0, 0x9f, 0x80, 0x0, 0x3, 0xfa, 0x0,
    0x0, 0x0, 0x2f, 0xa2, 0xfe, 0x2, 0x92, 0x0,
    0xcf, 0x50, 0x78, 0x4, 0xf9, 0x0, 0x4c, 0x50,
    0x6f, 0x72, 0xfe, 0x0, 0x0, 0x1, 0xff, 0x10,
    0xee, 0x6, 0xf8, 0x0, 0x9f, 0x50, 0xbf, 0x32,
    0xfe, 0x0, 0x0, 0x5, 0xfc, 0x1, 0xfb, 0x8,
    0xf7, 0x0, 0xdf, 0x0, 0x27, 0x2, 0xfe, 0x0,
    0x0, 0xa, 0xf7, 0x4, 0xf7, 0x9, 0xf5, 0x1,
    0xfc, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0xf,
    0xf3, 0x8, 0xf3, 0xc, 0xf3, 0x6, 0xf7, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x5f, 0xe0, 0xe,
    0xe0, 0xf, 0xf0, 0xc, 0xf1, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0xcf, 0x80, 0x5f, 0x80, 0x2f,
    0xf0, 0x4f, 0xb0, 0x0, 0x0, 0x2, 0xfe, 0x0,
    0x4, 0xff, 0x10, 0x3c, 0x10, 0x7f, 0xf5, 0x29,
    0x40, 0x0, 0x0, 0x2, 0xfe, 0x0, 0xd, 0xf9,
    0x0, 0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x7f, 0xf1, 0x0, 0x0,
    0x3, 0xff, 0xef, 0x20, 0x0, 0x0, 0x0, 0x2,
    0xfe, 0x4, 0xff, 0x70, 0x0, 0x0, 0xb, 0xf9,
    0x6f, 0xb0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x8,
    0xfb, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0xe, 0xf4,
    0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x61, 0x0,
    0x0, 0x3, 0xff, 0x80, 0x6, 0xfe, 0x10, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xfc, 0x0, 0x0, 0xbf, 0xd1, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0x0, 0x3a, 0xff, 0xd1, 0x0,
    0x0, 0x1d, 0xfe, 0x50, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x3, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xf3, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0,
    0x9c, 0x40, 0x0, 0x0, 0x0, 0x0, 0x7, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+626B "扫" */
    0x0, 0x0, 0x5, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x20, 0x0, 0x0,
    0xf, 0xf2, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0xff, 0x20,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x12, 0x22, 0x2f, 0xf4, 0x22, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x17, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0xff, 0x20, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0xf,
    0xf6, 0x8c, 0xf2, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x1, 0x48, 0xff, 0xff, 0xff,
    0x43, 0x44, 0x44, 0x44, 0x44, 0x44, 0x5f, 0xf1,
    0x8d, 0xff, 0xff, 0xfe, 0xa6, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x18, 0xff, 0xfb,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf1, 0x37, 0x30, 0xf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x10, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0xf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0x0, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0xf, 0xf2,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x1, 0xff, 0x20, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x8,
    0xff, 0xff, 0xf0, 0x0, 0x1, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0xff, 0x10, 0x2f, 0xff, 0xc4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,

    /* U+63CF "描" */
    0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x3,
    0x30, 0x0, 0x0, 0x23, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfe, 0x0, 0x0, 0x0, 0x2, 0xff, 0x10,
    0x0, 0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xe0, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x10, 0x0, 0xe, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0,
    0x0, 0x2f, 0xf1, 0x0, 0x0, 0xef, 0x40, 0x0,
    0x0, 0x0, 0x3, 0xfe, 0x0, 0x5, 0xee, 0xee,
    0xff, 0xee, 0xee, 0xef, 0xfe, 0xee, 0xe4, 0x1,
    0x22, 0x5f, 0xe2, 0x22, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0xdf, 0xff,
    0xff, 0xff, 0xf2, 0x22, 0x24, 0xff, 0x32, 0x22,
    0x2e, 0xf5, 0x22, 0x20, 0xd, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x10, 0x0, 0xe, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0,
    0x2f, 0xf1, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xe0, 0x0, 0x0, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc2, 0x0, 0x0, 0x3, 0xfe,
    0x0, 0x10, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x3f, 0xe7, 0xcf,
    0x1, 0xff, 0x33, 0x33, 0x6f, 0xd3, 0x33, 0x3f,
    0xf3, 0x0, 0x0, 0x28, 0xff, 0xff, 0xf1, 0x1f,
    0xf0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0xef, 0x30,
    0x17, 0xcf, 0xff, 0xfe, 0x93, 0x1, 0xff, 0x0,
    0x0, 0x4f, 0xc0, 0x0, 0xe, 0xf3, 0x1, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x4,
    0xfc, 0x0, 0x0, 0xef, 0x30, 0xd, 0xb6, 0x4f,
    0xe0, 0x0, 0x1, 0xff, 0x33, 0x33, 0x6f, 0xd3,
    0x33, 0x3e, 0xf3, 0x0, 0x0, 0x3, 0xfe, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x1,
    0xff, 0xcc, 0xcc, 0xdf, 0xfc, 0xcc, 0xcf, 0xf3,
    0x0, 0x0, 0x3, 0xfe, 0x0, 0x0, 0x1f, 0xf0,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0xef, 0x30, 0x0,
    0x0, 0x3f, 0xe0, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x4f, 0xc0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0x3,
    0xfe, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x4, 0xfc,
    0x0, 0x0, 0xef, 0x30, 0x0, 0x0, 0x3f, 0xe0,
    0x0, 0x1, 0xff, 0x11, 0x11, 0x4f, 0xc1, 0x11,
    0x1e, 0xf3, 0x0, 0x1, 0x15, 0xfd, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x5, 0xff, 0xff, 0xb0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x1f, 0xfe, 0xb2, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+65E0 "无" */
    0x0, 0x13, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4e, 0xf8, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x30, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfc, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x77, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf3, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfd,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x70, 0x7f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf1, 0x7, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf8, 0x0, 0x7f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xfe, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x3b, 0x50, 0x0, 0x0, 0x0, 0x1d, 0xff, 0x30,
    0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0x60, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0,
    0x0, 0x4e, 0xff, 0x70, 0x0, 0x0, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x1, 0xaf,
    0xff, 0x60, 0x0, 0x0, 0x5, 0xff, 0x63, 0x33,
    0x33, 0x35, 0xef, 0x80, 0x29, 0xff, 0xfd, 0x30,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x1, 0xef, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0xff, 0xc4,
    0x0, 0x3, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6700 "最" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xef, 0xb9,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x9c, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xb8, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x8c, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xef, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x8a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xad, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd3,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0x2f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfd, 0xcc, 0xcc, 0xcf,
    0xf0, 0xbe, 0xee, 0xee, 0xee, 0xee, 0xed, 0x20,
    0x0, 0xd, 0xff, 0xee, 0xee, 0xef, 0xf0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0xd,
    0xf4, 0x0, 0x0, 0x2f, 0xf0, 0x6, 0xfa, 0x0,
    0x0, 0x7, 0xfa, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0x2f, 0xf0, 0x0, 0xef, 0x30, 0x0, 0x1e,
    0xf2, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x6f, 0xc0, 0x0, 0xbf, 0x90, 0x0,
    0x0, 0xd, 0xfc, 0xbb, 0xbb, 0xcf, 0xf0, 0x0,
    0xc, 0xf9, 0x8, 0xfd, 0x0, 0x0, 0x0, 0xd,
    0xf4, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x1, 0xef,
    0xbf, 0xf3, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x2,
    0x45, 0x8f, 0xfc, 0x90, 0x0, 0x3f, 0xff, 0x50,
    0x0, 0x0, 0x69, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x5, 0xdf, 0xff, 0xe6, 0x0, 0x0,
    0xbf, 0xff, 0xfd, 0xba, 0x86, 0x6f, 0xf0, 0x5,
    0xbf, 0xfa, 0x2b, 0xff, 0xd7, 0x20, 0x34, 0x31,
    0x0, 0x0, 0x0, 0x2f, 0xf0, 0xef, 0xfc, 0x30,
    0x0, 0x5d, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf0, 0x6a, 0x30, 0x0, 0x0, 0x0,
    0x4a, 0x90,

    /* U+672C "本" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x5, 0x55,
    0x55, 0x55, 0x56, 0xff, 0x9f, 0xf9, 0xff, 0x55,
    0x55, 0x55, 0x55, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfc, 0x3f, 0xf3, 0xef, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5,
    0x3f, 0xf2, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xd0, 0x3f, 0xf2,
    0x1f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x50, 0x3f, 0xf2, 0x7, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xfc, 0x0, 0x3f, 0xf2, 0x0, 0xef, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0,
    0x3f, 0xf2, 0x0, 0x5f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x80, 0x0, 0x3f, 0xf2,
    0x0, 0xa, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xfd, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x1,
    0xef, 0xd1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x3f, 0xfc,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0x40, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x6, 0xff, 0xd1, 0x0,
    0x1, 0xcf, 0xf7, 0x55, 0x55, 0x55, 0x7f, 0xf7,
    0x55, 0x55, 0x55, 0x9f, 0xfe, 0x30, 0x5e, 0xff,
    0x63, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x28, 0xff, 0xf7, 0x5f, 0xf5, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x6f, 0xf3, 0x5, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x2, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+673A "机" */
    0x0, 0x0, 0x0, 0x59, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xa0, 0x0, 0x0, 0x4, 0x44, 0x44,
    0x44, 0x44, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xa0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xa0, 0x0, 0x0, 0x2f, 0xfd, 0xdd, 0xdd, 0xef,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xa0,
    0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xa0, 0x0,
    0x0, 0x2f, 0xf0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x2f, 0xf0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x2f,
    0xf0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x1,
    0x33, 0x33, 0xff, 0xb3, 0x33, 0x30, 0x2f, 0xf0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xa0, 0x0, 0x0, 0x2f, 0xf0, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xa0, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xf3, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0x4f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfe,
    0x20, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xaf, 0xef, 0xe1,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x3, 0xfa, 0x9f, 0xa9, 0xfd, 0x10,
    0x4f, 0xe0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0xa, 0xf4, 0x9f, 0xa0, 0xcf, 0xc0, 0x5f,
    0xc0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x2f, 0xd0, 0x9f, 0xa0, 0x2f, 0xc0, 0x7f, 0xa0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0x9f, 0xa0, 0x6, 0x20, 0xaf, 0x80, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0x6, 0xfe, 0x0,
    0x9f, 0xa0, 0x0, 0x0, 0xdf, 0x50, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0x9f,
    0xa0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0x4f,
    0xe0, 0x4, 0x20, 0xb, 0xb0, 0x0, 0x9f, 0xa0,
    0x0, 0x6, 0xfd, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x7, 0xf3, 0x2, 0x10, 0x0, 0x9f, 0xa0, 0x0,
    0xc, 0xf7, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x7,
    0xf3, 0x0, 0x0, 0x0, 0x9f, 0xa0, 0x0, 0x3f,
    0xf1, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x8, 0xf3,
    0x0, 0x0, 0x0, 0x9f, 0xa0, 0x0, 0xcf, 0x90,
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x9, 0xf2, 0x0,
    0x0, 0x0, 0x9f, 0xa0, 0x7, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x4f, 0xf2, 0x2c, 0xf1, 0x0, 0x0,
    0x0, 0x9f, 0xa0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x9f, 0xa0, 0x8, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xef, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+6B65 "步" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x31,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xd7, 0x0, 0x0,
    0xd, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xb, 0xf9, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0xb, 0xf9,
    0x0, 0x0, 0xd, 0xf9, 0x11, 0x11, 0x11, 0x11,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0,
    0xd, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11,
    0x1b, 0xf9, 0x11, 0x11, 0x1d, 0xf8, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x10, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x2f, 0xf6,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x4, 0x50, 0x0, 0xf, 0xf5, 0x0, 0x0,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xf9, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0xde,
    0x50, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe1, 0x0,
    0xf, 0xf5, 0x0, 0x0, 0x7, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x40, 0x0, 0xf, 0xf5,
    0x0, 0x0, 0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf7, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x1,
    0xdf, 0xe1, 0x0, 0x0, 0x0, 0x9, 0xff, 0x90,
    0x0, 0x0, 0xf, 0xf5, 0x0, 0x1c, 0xff, 0x30,
    0x0, 0x0, 0x1, 0xcf, 0xf9, 0x0, 0x0, 0x0,
    0xf, 0xf5, 0x1, 0xdf, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x90, 0x0, 0x0, 0x0, 0xf, 0xf5,
    0x5e, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xbd, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0xfc, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4a,
    0xff, 0xfe, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0x9e, 0xff, 0xfe, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x36,
    0x9d, 0xff, 0xff, 0xfc, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xb7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfe, 0xb8, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6D88 "消" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x77, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xca, 0x10, 0x0, 0x0, 0x12, 0x0, 0x0, 0x1f,
    0xf2, 0x0, 0x0, 0x25, 0x0, 0x0, 0x5f, 0xff,
    0x60, 0x0, 0x6f, 0xc0, 0x0, 0x1, 0xff, 0x20,
    0x0, 0xa, 0xfc, 0x0, 0x0, 0x2c, 0xff, 0xc1,
    0x1, 0xef, 0x70, 0x0, 0x1f, 0xf2, 0x0, 0x3,
    0xff, 0x50, 0x0, 0x0, 0x7, 0xff, 0xa0, 0x6,
    0xff, 0x20, 0x1, 0xff, 0x20, 0x0, 0xcf, 0xc0,
    0x0, 0x0, 0x0, 0x3, 0xd1, 0x0, 0xd, 0xfa,
    0x0, 0x1f, 0xf2, 0x0, 0x5f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x1,
    0xff, 0x20, 0x1e, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd8, 0x10, 0x1f, 0xf2,
    0x1, 0x7b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0x60, 0x0, 0x0, 0x0,
    0xbe, 0xee, 0xee, 0xef, 0xfe, 0xee, 0xee, 0xeb,
    0x0, 0xc, 0xff, 0xc2, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x9, 0xff, 0xf7, 0x0, 0x0, 0xdf, 0x84, 0x44,
    0x44, 0x44, 0x44, 0x48, 0xfd, 0x0, 0x0, 0x4,
    0xef, 0xf5, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x1, 0xbd,
    0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0,
    0xd, 0xf6, 0x11, 0x11, 0x11, 0x11, 0x11, 0x6f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfe, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xef, 0xd0, 0x0, 0x0,
    0x0, 0x3, 0x70, 0x0, 0xdf, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x60, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x0, 0x0, 0x0, 0xb, 0xfa, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x4, 0xff, 0x20, 0x0, 0xdf, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xfd, 0x0, 0x0,
    0x0, 0xcf, 0xa0, 0x0, 0xd, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x5f,
    0xf2, 0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xfd, 0x0, 0x0, 0xe, 0xfa, 0x0,
    0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xd0, 0x0, 0x8, 0xff, 0x20, 0x0, 0x0,
    0xdf, 0x60, 0x0, 0x0, 0x1, 0x10, 0x7, 0xfd,
    0x0, 0x0, 0xdf, 0x90, 0x0, 0x0, 0xd, 0xf6,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xb0, 0x0,
    0x1, 0xa1, 0x0, 0x0, 0x0, 0xdf, 0x60, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0xb2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7248 "版" */
    0x0, 0x0, 0x0, 0x0, 0x34, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x79, 0x20, 0xc, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x47, 0xad, 0x20, 0x0, 0xd, 0xf3,
    0x0, 0xcf, 0x50, 0x0, 0x13, 0x56, 0x8a, 0xcf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0xdf, 0x30, 0xc,
    0xf5, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xec,
    0x96, 0x20, 0x0, 0xd, 0xf3, 0x0, 0xcf, 0x50,
    0x0, 0xbf, 0xda, 0x86, 0x53, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x30, 0xc, 0xf5, 0x0, 0xb,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf3, 0x0, 0xcf, 0x50, 0x0, 0xbf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x30, 0xc, 0xf5, 0x0, 0xb, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x0,
    0xcf, 0x50, 0x0, 0xbf, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xcb, 0xbe, 0xfc,
    0xbb, 0x1b, 0xf8, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0xdf, 0x75, 0x55, 0x55, 0x55, 0xb, 0xfe,
    0xff, 0xdd, 0xdd, 0xdd, 0xff, 0x70, 0x0, 0xd,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x7c, 0xf0,
    0x0, 0x0, 0xd, 0xf4, 0x0, 0x0, 0xdf, 0x30,
    0x0, 0x0, 0x0, 0xb, 0xf6, 0x8f, 0x40, 0x0,
    0x1, 0xff, 0x10, 0x0, 0xe, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x64, 0xf9, 0x0, 0x0, 0x5f,
    0xc0, 0x0, 0x0, 0xef, 0xcc, 0xcc, 0xcc, 0x20,
    0xc, 0xf5, 0xf, 0xe0, 0x0, 0xb, 0xf7, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xdf,
    0x50, 0xaf, 0x40, 0x1, 0xff, 0x20, 0x0, 0x0,
    0xff, 0x54, 0x44, 0xff, 0x30, 0xe, 0xf4, 0x5,
    0xfb, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x1f, 0xf0,
    0x0, 0xf, 0xf3, 0x0, 0xff, 0x30, 0xe, 0xf3,
    0x1f, 0xf5, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0xff, 0x30, 0x1f, 0xf2, 0x0, 0x7f, 0xb9, 0xfd,
    0x0, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0xf, 0xf3,
    0x3, 0xff, 0x0, 0x0, 0xef, 0xff, 0x40, 0x0,
    0x0, 0x7, 0xfa, 0x0, 0x0, 0xff, 0x30, 0x6f,
    0xd0, 0x0, 0x6, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0xaf, 0x70, 0x0, 0xf, 0xf3, 0x9, 0xfa, 0x0,
    0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0, 0xe, 0xf4,
    0x0, 0x0, 0xff, 0x30, 0xdf, 0x70, 0x0, 0xbf,
    0xff, 0xfa, 0x0, 0x0, 0x2, 0xff, 0x0, 0x0,
    0xf, 0xf3, 0x2f, 0xf2, 0x1, 0xcf, 0xf5, 0x5f,
    0xfb, 0x10, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0xff,
    0x38, 0xfd, 0x6, 0xff, 0xf5, 0x0, 0x6f, 0xfe,
    0x50, 0xe, 0xf4, 0x0, 0x0, 0xf, 0xf3, 0xef,
    0x68, 0xff, 0xd3, 0x0, 0x0, 0x4e, 0xff, 0x50,
    0x3b, 0x0, 0x0, 0x0, 0xff, 0x35, 0xd0, 0xc,
    0x80, 0x0, 0x0, 0x0, 0x2b, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7387 "率" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6e, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0x54, 0x44, 0x44, 0x44, 0x44, 0x41,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x8, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xff, 0xec, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x30, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7d, 0x50, 0x0, 0x0, 0x1d,
    0xf6, 0x0, 0x1d, 0xe3, 0x0, 0x9, 0xe5, 0x0,
    0x0, 0x9f, 0xfb, 0x10, 0x0, 0xcf, 0x70, 0x0,
    0xcf, 0xb0, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x3,
    0xef, 0xe3, 0x1d, 0xff, 0xde, 0xef, 0xfb, 0x0,
    0x1c, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x1b, 0xf7,
    0xe, 0xff, 0xfe, 0xff, 0xb0, 0x0, 0xcf, 0xc2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0x3, 0x20,
    0xb, 0xfb, 0x0, 0x0, 0x5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0x40, 0x1, 0xcf, 0xa1,
    0xbd, 0x10, 0x36, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xdf, 0xa0, 0x3e, 0xf8, 0x0, 0x6f, 0xc3,
    0xff, 0xc3, 0x0, 0x0, 0x0, 0x18, 0xef, 0xf9,
    0x17, 0xff, 0x95, 0x67, 0x9f, 0xf8, 0x3c, 0xff,
    0x90, 0x0, 0xa, 0xff, 0xf9, 0x10, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x7f, 0xfd, 0x30,
    0x7, 0xf9, 0x20, 0x0, 0x6e, 0xb9, 0x86, 0x43,
    0x10, 0x6f, 0xb0, 0x3, 0xdf, 0xb0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf6, 0x0, 0x5,
    0x0, 0x0, 0x17, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x1f, 0xf7, 0x11, 0x11, 0x11, 0x11, 0x11, 0x10,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7801 "码" */
    0x2, 0x66, 0x66, 0x66, 0x66, 0x65, 0x2, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x42, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x4, 0xbb, 0xbe,
    0xfd, 0xbb, 0xba, 0x8, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x70, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0,
    0x0, 0x17, 0x50, 0x0, 0x0, 0xc, 0xf6, 0x0,
    0x0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x0, 0x4,
    0xfd, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x9f, 0x80, 0x0, 0x0, 0x0, 0x5f, 0xc0,
    0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0xe,
    0xf3, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0x90, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x0, 0x8f, 0xc6, 0x66, 0x66,
    0x0, 0xa, 0xf7, 0x0, 0x0, 0x5, 0xfd, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0xbf, 0x50, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0,
    0x6, 0xff, 0xda, 0xaa, 0xff, 0x10, 0xd, 0xf4,
    0x0, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x0, 0xef,
    0xf8, 0x0, 0xf, 0xf1, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x8f, 0xff, 0x80,
    0x0, 0xff, 0x10, 0x1e, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xef, 0xf1, 0xd, 0xfc, 0xf8, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x0, 0x6c, 0x6f, 0x80, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xe0,
    0x1, 0x26, 0xf8, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfc, 0x0, 0x0,
    0x6f, 0x80, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xa0, 0x0, 0x6, 0xf8,
    0x0, 0xf, 0xf1, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x9, 0xf8, 0x0, 0x0, 0x6f, 0x80, 0x0,
    0xff, 0x19, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xcf, 0x60, 0x0, 0x6, 0xf9, 0x22, 0x2f, 0xf1,
    0x1, 0x11, 0x11, 0x11, 0x11, 0x10, 0xf, 0xf3,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x0,
    0x6, 0xfe, 0xdd, 0xdd, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x6f,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xf8, 0x0, 0x0, 0x6, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x14, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xfc, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+786E "确" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0xcc, 0xcc, 0xcc, 0xcc,
    0xb0, 0x0, 0x4f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0xb,
    0xff, 0xdd, 0xdd, 0xdd, 0x70, 0x0, 0x5, 0x55,
    0xcf, 0xa5, 0x55, 0x40, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0x0, 0xdf, 0x60, 0x0, 0x3, 0xff, 0x50,
    0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0x8f,
    0xc0, 0x0, 0x0, 0xcf, 0xa0, 0x0, 0x0, 0x0,
    0x4f, 0xd0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x9f, 0xd0, 0x0, 0x0, 0x0, 0x8, 0xf9, 0x0,
    0x0, 0x2e, 0xff, 0xcc, 0xcc, 0xcf, 0xfd, 0xcc,
    0xcc, 0xb0, 0x0, 0xdf, 0x40, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x2f, 0xf0, 0x0, 0x1, 0xed, 0xdf, 0x63, 0x33,
    0xcf, 0x63, 0x33, 0x5f, 0xe0, 0x7, 0xfe, 0x66,
    0x66, 0x64, 0x3d, 0xf4, 0x0, 0xc, 0xf3, 0x0,
    0x3, 0xfe, 0x0, 0xef, 0xff, 0xff, 0xff, 0x0,
    0xdf, 0x40, 0x0, 0xcf, 0x30, 0x0, 0x3f, 0xe0,
    0x4f, 0xff, 0xee, 0xef, 0xf0, 0xd, 0xf5, 0x11,
    0x1c, 0xf4, 0x11, 0x14, 0xfe, 0xd, 0xff, 0xb0,
    0x0, 0xff, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe7, 0xff, 0xfb, 0x0, 0xf, 0xf0,
    0xd, 0xfd, 0xcc, 0xcf, 0xfc, 0xcc, 0xcc, 0xfe,
    0x9f, 0xdf, 0xb0, 0x0, 0xff, 0x0, 0xdf, 0x40,
    0x0, 0xcf, 0x30, 0x0, 0x3f, 0xe2, 0xe5, 0xfb,
    0x0, 0xf, 0xf0, 0xe, 0xf4, 0x0, 0xc, 0xf3,
    0x0, 0x3, 0xfe, 0x2, 0x3f, 0xb0, 0x0, 0xff,
    0x0, 0xef, 0x30, 0x0, 0xcf, 0x30, 0x0, 0x3f,
    0xe0, 0x3, 0xfb, 0x0, 0xf, 0xf0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x3f,
    0xb0, 0x0, 0xff, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x3, 0xfb, 0x0, 0xf,
    0xf0, 0x4f, 0xd0, 0x0, 0xc, 0xf4, 0x0, 0x4,
    0xfe, 0x0, 0x3f, 0xc2, 0x22, 0xff, 0x8, 0xfa,
    0x0, 0x0, 0xcf, 0x30, 0x0, 0x3f, 0xe0, 0x3,
    0xff, 0xff, 0xff, 0xf0, 0xcf, 0x60, 0x0, 0xc,
    0xf3, 0x0, 0x3, 0xfe, 0x0, 0x3f, 0xfd, 0xdd,
    0xdc, 0x3f, 0xf1, 0x0, 0x0, 0xcf, 0x30, 0x0,
    0x3f, 0xe0, 0x3, 0xfb, 0x0, 0x0, 0xb, 0xfa,
    0x0, 0x0, 0xc, 0xf3, 0x0, 0x4, 0xfe, 0x0,
    0x3f, 0xb0, 0x0, 0x6, 0xff, 0x30, 0x0, 0x0,
    0xcf, 0x37, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x4e, 0x80, 0x0, 0x0, 0x1, 0x10, 0x3f,
    0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7EBF "线" */
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x39, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x1c, 0x80, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x5e, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x1, 0x8f, 0xf9, 0x0,
    0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x3, 0xc2, 0x0, 0x0, 0x0,
    0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xe0,
    0x0, 0x0, 0x24, 0x10, 0x0, 0x5, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x8b, 0xdf,
    0xff, 0x50, 0x0, 0xd, 0xf7, 0x0, 0x2e, 0x70,
    0x58, 0xac, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x50,
    0x0, 0x7f, 0xd0, 0x0, 0xaf, 0xc0, 0xcf, 0xff,
    0xff, 0xfa, 0x85, 0x30, 0x0, 0x0, 0x2, 0xff,
    0x30, 0x4, 0xff, 0x20, 0x68, 0x64, 0x1f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xfd, 0x9b, 0xcf,
    0xf8, 0x0, 0x0, 0x0, 0xe, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0xd, 0xf5, 0x0, 0x1, 0x47, 0xa1,
    0xd, 0xa7, 0x56, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0x8b, 0xef, 0xff, 0xf4, 0x0, 0x0,
    0xd, 0xf8, 0x0, 0x0, 0x3, 0x69, 0xcf, 0xff,
    0xff, 0xff, 0xc9, 0x61, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xfe, 0x85, 0x20,
    0x0, 0x0, 0x0, 0x6, 0xfe, 0x20, 0x0, 0x4,
    0xfe, 0xb8, 0x56, 0xfe, 0x0, 0x0, 0x6, 0x60,
    0x0, 0x4f, 0xf4, 0x14, 0x7a, 0x70, 0x10, 0x0,
    0x2, 0xff, 0x0, 0x0, 0x3f, 0xf5, 0x4, 0xff,
    0xfe, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0xff,
    0x30, 0x1, 0xef, 0x90, 0x1f, 0xff, 0xff, 0xeb,
    0x74, 0x10, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x1d,
    0xfc, 0x0, 0xc, 0xd9, 0x52, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xb2, 0xdf, 0xd1, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x60, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7b,
    0xff, 0xe0, 0x0, 0x0, 0x5, 0xef, 0xfe, 0x0,
    0x0, 0x20, 0x1, 0x59, 0xef, 0xff, 0xfc, 0x70,
    0x0, 0x4, 0xcf, 0xfe, 0xff, 0x40, 0x0, 0xc9,
    0x2f, 0xff, 0xfe, 0xa5, 0x10, 0x0, 0x6, 0xcf,
    0xff, 0x70, 0xbf, 0xd0, 0x0, 0xed, 0xf, 0xd8,
    0x30, 0x0, 0x0, 0x2a, 0xff, 0xff, 0x91, 0x0,
    0x2f, 0xfd, 0x56, 0xfa, 0x1, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xfe, 0x71, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x4c, 0xfe, 0x80,

    /* U+7F6E "置" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x4f, 0xf9, 0x99,
    0x9b, 0xfe, 0x99, 0x99, 0xbf, 0xe9, 0x99, 0x9a,
    0xff, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x5, 0xfb,
    0x0, 0x0, 0x5f, 0xb0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x5, 0xfb, 0x0, 0x0,
    0x5f, 0xb0, 0x0, 0x3, 0xff, 0x0, 0x0, 0x4f,
    0xfb, 0xbb, 0xbc, 0xfe, 0xbb, 0xbb, 0xcf, 0xeb,
    0xbb, 0xbc, 0xff, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x22, 0x22, 0x22, 0x22, 0x22, 0x7f, 0xe2,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x7, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xef, 0xca, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x9e, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf7, 0x66, 0x66,
    0x66, 0x66, 0x66, 0x66, 0x6d, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf7,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x6d, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf7, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x6d, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xa5,

    /* U+8BA4 "认" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x55, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x80, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x13, 0x33,
    0x37, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfc, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x8, 0xfb,
    0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x61, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0xb, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x5a,
    0x0, 0x9, 0xfc, 0x0, 0x5f, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xd1, 0xaf, 0xf1, 0x2,
    0xff, 0x50, 0x0, 0xdf, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xef, 0xe5, 0x0, 0xbf, 0xe0,
    0x0, 0x5, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xb1, 0x0, 0x6f, 0xf5, 0x0, 0x0,
    0xb, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x70, 0x0, 0x3f, 0xfb, 0x0, 0x0, 0x0, 0x2f,
    0xfc, 0x0, 0x0, 0x0, 0xb, 0xfd, 0x30, 0x0,
    0x4f, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xfc,
    0x10, 0x0, 0x0, 0x1b, 0x10, 0x0, 0x4f, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9d, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8BBE "设" */
    0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0, 0x22,
    0x22, 0x22, 0x22, 0x20, 0x0, 0x0, 0x0, 0xc,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0x0, 0x0,
    0x0, 0xff, 0x10, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xa0, 0x0, 0x1, 0xff,
    0x0, 0x0, 0xd, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0x40, 0x0, 0x2, 0xff, 0x0, 0x0,
    0xd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf8, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1,
    0x0, 0x0, 0xc, 0xf8, 0x33, 0x31, 0x7f, 0xff,
    0xff, 0xe0, 0x0, 0x1b, 0xff, 0x60, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xf6, 0x7f, 0xff, 0xff, 0xe0,
    0x5, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x1, 0x9b,
    0xcc, 0xb5, 0x13, 0x33, 0x6f, 0xe0, 0x0, 0xce,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x2,
    0xef, 0x52, 0x22, 0x22, 0x22, 0x3f, 0xf4, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x8f, 0xb0,
    0x0, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x1e, 0xf5, 0x0, 0x0,
    0x3, 0xff, 0x40, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x6, 0xfe, 0x20, 0x0, 0xd, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0xbf, 0xd1, 0x0, 0xbf, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x1b, 0x80, 0x0, 0x1d,
    0xfd, 0x2b, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe4, 0xef, 0xd0, 0x0, 0x2, 0xef, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x8f, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x80, 0x0,
    0x0, 0x4c, 0xff, 0xff, 0xfd, 0x50, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xe4, 0x0, 0x2, 0x7d, 0xff,
    0xf7, 0x7, 0xff, 0xfe, 0x82, 0x0, 0x0, 0x4,
    0xfc, 0x10, 0x4, 0xdf, 0xff, 0xf8, 0x10, 0x0,
    0x2a, 0xff, 0xff, 0xe7, 0x0, 0x0, 0x70, 0x0,
    0x0, 0xdf, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x18,
    0xef, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x40,

    /* U+8BC6 "识" */
    0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x90, 0x0, 0x0, 0x0, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x0, 0x0, 0x4f, 0xfb, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x3, 0xef, 0xc1, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x3e, 0xfc, 0x0, 0x0, 0xef, 0x51,
    0x11, 0x11, 0x11, 0x17, 0xfe, 0x0, 0x0, 0x0,
    0x2, 0xfa, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x6,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x7f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x6, 0xfe, 0x0, 0x8f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x6,
    0xfe, 0x0, 0x13, 0x33, 0x6f, 0xe0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x23, 0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0xb9, 0x30, 0x0, 0x3, 0x98,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x5, 0x10,
    0x3, 0xff, 0x40, 0x0, 0x5, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x8f, 0x80, 0x9, 0xfe,
    0x0, 0x0, 0x0, 0xcf, 0xb0, 0x0, 0x0, 0x0,
    0x4f, 0xfb, 0xff, 0x70, 0x1f, 0xf7, 0x0, 0x0,
    0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xe4, 0x0, 0x9f, 0xe0, 0x0, 0x0, 0x0, 0xc,
    0xfc, 0x0, 0x0, 0x0, 0xcf, 0xfd, 0x20, 0x3,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x7, 0xff, 0xa0, 0x0, 0xd, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xc0, 0x0, 0x7,
    0xf8, 0x0, 0x0, 0xbf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x30, 0x0,
    0x0, 0x8f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8FDB "进" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x2, 0x53,
    0x0, 0x0, 0x15, 0x40, 0x0, 0x0, 0x2, 0xdd,
    0x20, 0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0,
    0x5f, 0xe0, 0x0, 0x0, 0x1, 0xcf, 0xe2, 0x0,
    0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0x0, 0x1c, 0xfe, 0x20, 0x0, 0x0,
    0x8, 0xfb, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xd1, 0x0, 0x0, 0x8, 0xfb,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xf8, 0x6, 0x88, 0x8c, 0xfd, 0x88, 0x88,
    0xaf, 0xf8, 0x88, 0x60, 0x0, 0x0, 0x4, 0x90,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xaa,
    0xad, 0xfe, 0xaa, 0xaa, 0xcf, 0xfa, 0xaa, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfb,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0,
    0x5f, 0xe0, 0x0, 0x0, 0x2, 0x22, 0x22, 0x10,
    0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x8, 0xfb, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x9, 0xfa,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x4f, 0xe0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x4f, 0xe0, 0x2, 0x22,
    0x2e, 0xf6, 0x22, 0x22, 0x7f, 0xe2, 0x22, 0x21,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x2f, 0xf1,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x0,
    0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x1, 0xff, 0x60, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0xa,
    0xff, 0x10, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x8f, 0xf6, 0x0,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf0, 0x3, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x5f, 0xe0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xfd,
    0x40, 0x49, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xb0,
    0x0, 0x0, 0x6, 0xff, 0xb3, 0x7f, 0xfc, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x7f, 0xfa, 0x0, 0x2, 0xbf, 0xff, 0xed, 0xcb,
    0xbb, 0xbb, 0xcc, 0xde, 0xff, 0xfb, 0x3f, 0xc0,
    0x0, 0x0, 0x3, 0xae, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x13, 0x45, 0x55, 0x55, 0x54, 0x43,
    0x32, 0x10,

    /* U+9053 "道" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xd9, 0x0, 0x0, 0x0,
    0x9, 0xb5, 0x0, 0x0, 0x2, 0xa2, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x50, 0x0, 0x0, 0x2f, 0xf4,
    0x0, 0x0, 0xa, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0xbf, 0xe2, 0x0, 0x0, 0x0, 0x1e, 0xa2,
    0x0, 0x5, 0xfe, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xfd, 0x10, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x1, 0xef, 0xb0,
    0xbd, 0xdd, 0xdd, 0xdd, 0xff, 0xed, 0xdd, 0xdd,
    0xdd, 0xd3, 0x0, 0x0, 0x3f, 0x70, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xcc, 0xce, 0xfe, 0xcc,
    0xcc, 0xcc, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x27, 0x77, 0x77, 0x60, 0x0, 0xaf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0,
    0x6f, 0xff, 0xff, 0xe0, 0x0, 0xaf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x39, 0x99,
    0xbf, 0xe0, 0x0, 0xaf, 0xda, 0xaa, 0xaa, 0xaa,
    0xaa, 0xcf, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0xe0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0xaf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0,
    0x0, 0x0, 0x3f, 0xe0, 0x0, 0xaf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0x0,
    0x3f, 0xe0, 0x0, 0xaf, 0xc9, 0x99, 0x99, 0x99,
    0x99, 0xcf, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0xe0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0xaf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0,
    0x0, 0x0, 0x3f, 0xe0, 0x0, 0xaf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0x0,
    0x3f, 0xe0, 0x0, 0xaf, 0xda, 0xaa, 0xaa, 0xaa,
    0xaa, 0xcf, 0xb0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x8, 0xff, 0xfc, 0x20, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0x0,
    0x0, 0xbf, 0xfa, 0xdf, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x30, 0x7, 0xff, 0xea, 0x64, 0x22, 0x11, 0x12,
    0x23, 0x34, 0x56, 0x85, 0x7f, 0xf3, 0x0, 0x0,
    0x2a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xc, 0x50, 0x0, 0x0, 0x0, 0x16,
    0x9b, 0xde, 0xee, 0xee, 0xdd, 0xcc, 0xba, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9891 "频" */
    0x0, 0x0, 0x0, 0x5, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x48, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x85, 0x0, 0x6, 0xb3, 0x0, 0xff,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x9f, 0x40, 0xf, 0xfb, 0xbb,
    0xb4, 0x36, 0x66, 0x66, 0xef, 0x96, 0x66, 0x64,
    0x0, 0x9, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x40, 0xf, 0xf3, 0x33, 0x31, 0x0, 0x0,
    0x7, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf4,
    0x0, 0xff, 0x0, 0x0, 0x0, 0x57, 0x77, 0xef,
    0xa7, 0x77, 0x76, 0x0, 0x0, 0x9f, 0x40, 0xf,
    0xf0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x9, 0xf4, 0x0, 0xff, 0x0,
    0x0, 0x0, 0xdf, 0x76, 0x66, 0x66, 0x67, 0xfe,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd, 0xf1, 0x0, 0x11, 0x0, 0x2f, 0xe0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xdf,
    0x10, 0xf, 0xe0, 0x2, 0xfe, 0x0, 0x11, 0x11,
    0x11, 0x9f, 0x81, 0x11, 0x11, 0xd, 0xf1, 0x0,
    0xfe, 0x0, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0x9,
    0xf7, 0x0, 0x0, 0x0, 0xdf, 0x10, 0xf, 0xd0,
    0x2, 0xfe, 0x0, 0x0, 0x2d, 0x80, 0x9f, 0x70,
    0x2, 0x83, 0xd, 0xf1, 0x0, 0xfd, 0x0, 0x2f,
    0xe0, 0x0, 0x7, 0xf8, 0x9, 0xf7, 0x0, 0x8f,
    0x90, 0xdf, 0x10, 0x1f, 0xc0, 0x2, 0xfe, 0x0,
    0x0, 0xdf, 0x30, 0x9f, 0x70, 0xd, 0xf4, 0xd,
    0xf1, 0x1, 0xfc, 0x0, 0x2f, 0xe0, 0x0, 0x4f,
    0xc0, 0x9, 0xf7, 0x3, 0xfe, 0x0, 0xdf, 0x10,
    0x3f, 0xb0, 0x2, 0xfe, 0x0, 0xd, 0xf5, 0x0,
    0x9f, 0x70, 0xaf, 0x80, 0xd, 0xf1, 0x4, 0xf9,
    0x0, 0x2f, 0xe0, 0x7, 0xfd, 0x0, 0x9, 0xf7,
    0x3f, 0xf2, 0x0, 0xdf, 0x10, 0x7f, 0x70, 0x2,
    0xfe, 0x0, 0x4d, 0x30, 0x0, 0x9f, 0x8c, 0xf9,
    0x0, 0xd, 0xf1, 0x9, 0xf4, 0x0, 0x2f, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0x4b, 0xfe, 0x10, 0x0,
    0xce, 0x10, 0xef, 0x10, 0x2, 0xdb, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x4f, 0xc3, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x70, 0x0, 0x0, 0x0, 0xd, 0xf6,
    0xdf, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff,
    0x70, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x1, 0xbf,
    0xe4, 0x0, 0x0, 0x1, 0x8f, 0xfe, 0x40, 0x0,
    0x0, 0x0, 0x4d, 0xfe, 0x10, 0x0, 0x8f, 0xf7,
    0x0, 0x2a, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x17,
    0xcf, 0xfb, 0x10, 0x0, 0x0, 0x5f, 0xfa, 0x1,
    0xef, 0xb3, 0x0, 0x0, 0x0, 0x2, 0xff, 0xc5,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xc0, 0x3, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x21, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 108, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 155, .box_w = 5, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 55, .adv_w = 228, .box_w = 10, .box_h = 9, .ofs_x = 2, .ofs_y = 15},
    {.bitmap_index = 100, .adv_w = 266, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 265, .adv_w = 266, .box_w = 14, .box_h = 29, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 468, .adv_w = 442, .box_w = 26, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 754, .adv_w = 326, .box_w = 19, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 963, .adv_w = 133, .box_w = 4, .box_h = 9, .ofs_x = 2, .ofs_y = 15},
    {.bitmap_index = 981, .adv_w = 162, .box_w = 7, .box_h = 31, .ofs_x = 2, .ofs_y = -6},
    {.bitmap_index = 1090, .adv_w = 162, .box_w = 7, .box_h = 31, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 1199, .adv_w = 224, .box_w = 12, .box_h = 10, .ofs_x = 1, .ofs_y = 14},
    {.bitmap_index = 1259, .adv_w = 266, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 1379, .adv_w = 133, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 1409, .adv_w = 167, .box_w = 9, .box_h = 3, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 1423, .adv_w = 133, .box_w = 5, .box_h = 5, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1436, .adv_w = 188, .box_w = 12, .box_h = 30, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 1616, .adv_w = 266, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1781, .adv_w = 266, .box_w = 13, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1924, .adv_w = 266, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2089, .adv_w = 266, .box_w = 15, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2254, .adv_w = 266, .box_w = 16, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2430, .adv_w = 266, .box_w = 16, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2606, .adv_w = 266, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2771, .adv_w = 266, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2936, .adv_w = 266, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3101, .adv_w = 266, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3266, .adv_w = 133, .box_w = 5, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3309, .adv_w = 133, .box_w = 6, .box_h = 23, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 3378, .adv_w = 266, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 3491, .adv_w = 266, .box_w = 15, .box_h = 10, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 3566, .adv_w = 266, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 3679, .adv_w = 228, .box_w = 12, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3811, .adv_w = 454, .box_w = 26, .box_h = 27, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 4162, .adv_w = 292, .box_w = 19, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4371, .adv_w = 315, .box_w = 16, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 4547, .adv_w = 306, .box_w = 18, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4745, .adv_w = 330, .box_w = 16, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 4921, .adv_w = 283, .box_w = 14, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5075, .adv_w = 265, .box_w = 13, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5218, .adv_w = 331, .box_w = 18, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5416, .adv_w = 349, .box_w = 16, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5592, .adv_w = 141, .box_w = 3, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5625, .adv_w = 257, .box_w = 13, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5768, .adv_w = 310, .box_w = 17, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5955, .adv_w = 261, .box_w = 13, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6098, .adv_w = 390, .box_w = 19, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6307, .adv_w = 347, .box_w = 16, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6483, .adv_w = 356, .box_w = 20, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6703, .adv_w = 304, .box_w = 15, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6868, .adv_w = 356, .box_w = 20, .box_h = 29, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 7158, .adv_w = 305, .box_w = 16, .box_h = 22, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7334, .adv_w = 286, .box_w = 16, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7510, .adv_w = 288, .box_w = 18, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7708, .adv_w = 346, .box_w = 17, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7895, .adv_w = 276, .box_w = 18, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8093, .adv_w = 421, .box_w = 26, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8379, .adv_w = 275, .box_w = 17, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8566, .adv_w = 255, .box_w = 17, .box_h = 22, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8753, .adv_w = 289, .box_w = 16, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8929, .adv_w = 162, .box_w = 7, .box_h = 30, .ofs_x = 3, .ofs_y = -6},
    {.bitmap_index = 9034, .adv_w = 188, .box_w = 12, .box_h = 30, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 9214, .adv_w = 162, .box_w = 6, .box_h = 30, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 9304, .adv_w = 266, .box_w = 13, .box_h = 13, .ofs_x = 2, .ofs_y = 10},
    {.bitmap_index = 9389, .adv_w = 268, .box_w = 17, .box_h = 2, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9406, .adv_w = 291, .box_w = 7, .box_h = 8, .ofs_x = 4, .ofs_y = 19},
    {.bitmap_index = 9434, .adv_w = 270, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9553, .adv_w = 297, .box_w = 15, .box_h = 24, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9733, .adv_w = 245, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9852, .adv_w = 298, .box_w = 15, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10032, .adv_w = 266, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10160, .adv_w = 156, .box_w = 10, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10285, .adv_w = 271, .box_w = 16, .box_h = 24, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 10477, .adv_w = 291, .box_w = 14, .box_h = 24, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10645, .adv_w = 132, .box_w = 4, .box_h = 24, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10693, .adv_w = 132, .box_w = 8, .box_h = 31, .ofs_x = -2, .ofs_y = -7},
    {.bitmap_index = 10817, .adv_w = 265, .box_w = 15, .box_h = 24, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10997, .adv_w = 136, .box_w = 6, .box_h = 24, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11069, .adv_w = 444, .box_w = 24, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11273, .adv_w = 293, .box_w = 14, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11392, .adv_w = 291, .box_w = 16, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11528, .adv_w = 298, .box_w = 15, .box_h = 24, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 11708, .adv_w = 298, .box_w = 15, .box_h = 24, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 11888, .adv_w = 186, .box_w = 10, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11973, .adv_w = 225, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12084, .adv_w = 181, .box_w = 11, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12205, .adv_w = 291, .box_w = 14, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 12324, .adv_w = 250, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12460, .adv_w = 385, .box_w = 24, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12664, .adv_w = 239, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12792, .adv_w = 250, .box_w = 16, .box_h = 24, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12984, .adv_w = 228, .box_w = 13, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13095, .adv_w = 162, .box_w = 9, .box_h = 30, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 13230, .adv_w = 130, .box_w = 3, .box_h = 34, .ofs_x = 3, .ofs_y = -8},
    {.bitmap_index = 13281, .adv_w = 162, .box_w = 8, .box_h = 30, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 13401, .adv_w = 266, .box_w = 15, .box_h = 5, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 13439, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13845, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 14251, .adv_w = 480, .box_w = 24, .box_h = 27, .ofs_x = 3, .ofs_y = -2},
    {.bitmap_index = 14575, .adv_w = 480, .box_w = 27, .box_h = 28, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 14953, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15345, .adv_w = 480, .box_w = 27, .box_h = 27, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15710, .adv_w = 480, .box_w = 28, .box_h = 27, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 16088, .adv_w = 480, .box_w = 27, .box_h = 27, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 16453, .adv_w = 480, .box_w = 26, .box_h = 27, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 16804, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 17196, .adv_w = 480, .box_w = 29, .box_h = 28, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 17602, .adv_w = 480, .box_w = 30, .box_h = 29, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18037, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 18443, .adv_w = 480, .box_w = 29, .box_h = 28, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 18849, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 19255, .adv_w = 480, .box_w = 27, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 19647, .adv_w = 480, .box_w = 29, .box_h = 29, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 20068, .adv_w = 480, .box_w = 29, .box_h = 27, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 20460, .adv_w = 480, .box_w = 28, .box_h = 27, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 20838, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 21230, .adv_w = 480, .box_w = 30, .box_h = 29, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 21665, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 22071, .adv_w = 480, .box_w = 29, .box_h = 29, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 22492, .adv_w = 480, .box_w = 29, .box_h = 29, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 22913, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 23305, .adv_w = 480, .box_w = 29, .box_h = 27, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 23697, .adv_w = 480, .box_w = 27, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 24089, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 24481, .adv_w = 480, .box_w = 28, .box_h = 27, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 24859, .adv_w = 480, .box_w = 29, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 25280, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 25672, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 26064, .adv_w = 480, .box_w = 28, .box_h = 27, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 26442, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 26848, .adv_w = 480, .box_w = 29, .box_h = 29, .ofs_x = 1, .ofs_y = -3}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x2c5, 0x34c, 0x37d, 0x3f1, 0x4d4, 0x528, 0x549,
    0x55e, 0x8dd, 0xa5f, 0xd61, 0xff8, 0x10d7, 0x11b4, 0x13bd,
    0x1521, 0x1732, 0x1852, 0x187e, 0x188c, 0x1cb7, 0x1eda, 0x239a,
    0x24d9, 0x2953, 0x29c0, 0x3011, 0x30c0, 0x3cf6, 0x3d10, 0x3d18,
    0x412d, 0x41a5, 0x49e3
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20142, .range_length = 18916, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 35, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 0, 0, 0, 3, 4, 3,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 6, 6, 0, 0, 0,
    0, 0, 7, 8, 9, 10, 11, 12,
    13, 0, 0, 14, 15, 16, 0, 0,
    10, 17, 10, 18, 19, 20, 21, 22,
    23, 24, 25, 26, 2, 27, 0, 0,
    0, 0, 28, 29, 30, 0, 31, 32,
    33, 34, 0, 0, 35, 36, 34, 34,
    29, 29, 37, 38, 39, 40, 37, 41,
    42, 43, 44, 45, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 0, 0, 0,
    2, 0, 3, 4, 0, 5, 6, 7,
    8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 9, 10, 0, 0, 0,
    11, 0, 12, 0, 13, 0, 0, 0,
    13, 0, 0, 14, 0, 0, 0, 0,
    13, 0, 13, 0, 15, 16, 17, 18,
    19, 20, 21, 22, 0, 23, 3, 0,
    0, 0, 24, 0, 25, 25, 25, 26,
    27, 0, 28, 29, 0, 0, 30, 30,
    25, 30, 25, 30, 31, 32, 33, 34,
    35, 36, 37, 38, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, -61, 0, -61, 0,
    0, 0, 0, -29, 0, -51, -5, 0,
    0, 0, 0, -5, 0, 0, 0, 0,
    -16, 0, 0, 0, 0, 0, -11, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 43, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -51, 0, -74,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -53, -11, -35, -18, 0,
    -49, 0, 0, 0, -6, 0, 0, 0,
    14, 0, 0, -24, 0, -18, -12, 0,
    -11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -11,
    -9, -26, 0, -10, -5, -14, -36, -11,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, -4, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -22, -5, -43, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -13,
    -17, 0, -5, 14, 14, 0, 0, 5,
    -11, 0, 0, 0, 0, 0, 0, 0,
    0, -26, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -14, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -29, 0, -50,
    0, 0, 0, 0, 0, 0, -14, -3,
    -5, 0, 0, -29, -8, -7, 0, 2,
    -7, -4, -22, 12, 0, -5, 0, 0,
    0, 0, 12, -7, -3, -4, -2, -2,
    -4, 0, 0, 0, 0, -16, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -9,
    -7, -13, 0, -3, -2, -2, -7, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -7, -5, -5, -7, 0,
    0, 0, 0, 0, 0, -14, 0, 0,
    0, 0, 0, 0, -15, -5, -13, -10,
    -7, -2, -2, -2, -4, -5, 0, 0,
    0, 0, -11, 0, 0, 0, 0, -14,
    -5, -7, -5, 0, -7, 0, 0, 0,
    0, -18, 0, 0, 0, -10, 0, 0,
    0, -5, 0, -21, 0, -13, 0, -5,
    -3, -9, -11, -11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, -13, 0, -5, 0, -16,
    -5, 0, 0, 0, 0, 0, -39, 0,
    -39, -37, 0, 0, 0, -20, -5, -74,
    -11, 0, 0, 2, 2, -13, 0, -16,
    0, -18, -7, 0, -13, 0, 0, -11,
    -11, -5, -9, -11, -9, -14, -9, -16,
    0, 0, 0, -15, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, -11,
    0, -7, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -13, 0, -13, 0, 0, 0,
    0, 0, 0, -22, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -11, 0, -22,
    0, -16, 0, 0, 0, 0, -4, -5,
    -11, 0, -5, -9, -7, -7, -5, 0,
    -9, 0, 0, 0, -4, 0, 0, 0,
    -5, 0, 0, -18, -8, -11, -9, -9,
    -11, -7, 0, -48, 0, -82, 0, -29,
    0, 0, 0, 0, -17, 1, -14, 0,
    -12, -64, -15, -41, -30, 0, -41, 0,
    -43, 0, -6, -7, -2, 0, 0, 0,
    0, -11, -5, -19, -18, 0, -19, 0,
    0, 0, 0, 0, -60, -18, -60, -41,
    0, 0, 0, -27, 0, -79, -5, -13,
    0, 0, 0, -13, -5, -42, 0, -23,
    -13, 0, -16, 0, 0, 0, -5, 0,
    0, 0, 0, -7, 0, -11, 0, 0,
    0, -5, 0, -17, 0, 0, 0, 0,
    0, -2, 0, -10, -7, -7, 0, 3,
    3, -2, 0, -5, 0, -2, -5, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -4, 0, 0, 0, -9,
    0, 7, 0, 0, 0, 0, 0, 0,
    0, -7, -7, -11, 0, 0, 0, 0,
    -7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -57, -39,
    -57, -48, -11, -11, 0, -22, -13, -68,
    -21, 0, 0, 0, 0, -11, -7, -29,
    0, -39, -36, -10, -39, 0, 0, -25,
    -32, -10, -25, -18, -18, -21, -18, -40,
    0, 0, 0, 0, -9, 0, -9, -17,
    0, 0, 0, -9, 0, -26, -5, 0,
    0, -2, 0, -5, -7, 0, 0, -2,
    0, 0, -5, 0, 0, 0, -2, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, -35, -10, -35, -25, 0, 0,
    0, -7, -5, -39, -5, 0, -5, 5,
    0, 0, 0, -10, 0, -12, -8, 0,
    -11, 0, 0, -11, -7, 0, -16, -5,
    -5, -8, -5, -13, 0, 0, 0, 0,
    -18, -5, -18, -16, 0, 0, 0, 0,
    -3, -35, -3, 0, 0, 0, 0, 0,
    0, -3, 0, -9, 0, 0, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -5, 0, -5, 0, -14,
    0, 0, 0, 0, 0, 1, -9, 0,
    -7, -11, -5, 0, 0, 0, 0, 0,
    0, -5, -4, -9, 0, 0, 0, 0,
    0, -9, -5, -9, -7, -5, -9, -7,
    0, 0, 0, 0, -49, -36, -49, -36,
    -13, -13, -4, -7, -7, -54, -9, -7,
    -5, 0, 0, 0, 0, -14, 0, -36,
    -22, 0, -32, 0, 0, -22, -22, -15,
    -18, -7, -13, -18, -7, -26, 0, 0,
    0, 0, 0, -18, 0, 0, 0, 0,
    0, -3, -11, -18, -16, 0, -5, -3,
    -3, 0, -7, -9, 0, -9, -11, -11,
    -8, 0, 0, 0, 0, -7, -12, -9,
    -9, -13, -9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -46, -16, -28, -16, 0,
    -39, 0, 0, 0, 0, 0, 18, 0,
    39, 0, 0, 0, 0, -11, -5, 0,
    7, 0, 0, 0, 0, -29, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, -13, 0, -9, -2, 0, -13, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, -16, 0, -14, -5, 3, -5, 0,
    0, 0, -7, 0, 0, 0, 0, -31,
    0, -10, 0, -2, -25, 0, -14, -8,
    0, 0, 0, 0, 0, 0, 0, -9,
    0, -2, -2, -9, -2, -3, 0, 0,
    0, 0, 0, -11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -11, 0, -7,
    0, 0, -13, 0, 0, -5, -11, 0,
    -5, 0, 0, 0, 0, -5, 0, 3,
    3, 4, 3, 0, 0, 0, 0, -18,
    0, 5, 0, 0, 0, 0, -4, 0,
    0, -11, -11, -13, 0, -9, -5, 0,
    -14, 0, -11, -8, 0, 0, -5, 0,
    0, 0, 0, -5, 0, 2, 2, -4,
    2, 0, 8, 22, 26, 0, -27, -7,
    -27, -8, 0, 0, 14, 0, 0, 0,
    0, 25, 0, 36, 25, 18, 32, 0,
    34, -11, -5, 0, -8, 0, -5, 0,
    -2, 0, 0, 7, 0, -2, 0, -7,
    0, 0, 8, -18, 0, 0, 0, 26,
    0, 0, -19, 0, 0, 0, 0, -14,
    0, 0, 0, 0, -7, 0, 0, -9,
    -7, 0, 0, 0, 20, 0, 0, 0,
    0, -2, -2, 0, 8, -7, 0, 0,
    0, -18, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, -13, 0, -5,
    0, 0, -9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -11,
    7, -23, 7, 0, 7, 7, -7, 0,
    0, 0, 0, -19, 0, 0, 0, 0,
    -6, 0, 0, -5, -10, 0, -5, 0,
    -5, 0, 0, -11, -7, 0, 0, -4,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -11,
    0, -7, 0, 0, -16, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -30, -13, -30, -18, 14, 14,
    0, -7, 0, -29, 0, 0, 0, 0,
    0, 0, 0, -5, 7, -13, -5, 0,
    -5, 0, 0, 0, -2, 0, 0, 14,
    10, 0, 14, -2, 0, 0, 0, -26,
    0, 5, 0, 0, 0, 0, -6, 0,
    0, 0, 0, -13, 0, -5, 0, 0,
    -11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 3, -14,
    3, 5, 8, 8, -14, 0, 0, 0,
    0, -7, 0, 0, 0, 0, -2, 0,
    0, -11, -7, 0, -5, 0, 0, 0,
    -5, -11, 0, 0, 0, -9, 0, 0,
    0, 0, 0, -6, -18, -4, -18, -11,
    0, 0, 0, -6, 0, -22, 0, -11,
    0, -5, 0, 0, -7, -5, 0, -11,
    -2, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, -13, 0, 0,
    0, -6, -22, 0, -22, -4, 0, 0,
    0, -2, 0, -16, 0, -13, 0, -5,
    0, -7, -13, 0, 0, -5, -2, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, -9, -7, 0, 0, -12,
    4, -7, -4, 0, 0, 4, 0, 0,
    -5, 0, -2, -18, 0, -8, 0, -5,
    -18, 0, 0, -5, -9, 0, 0, 0,
    0, 0, 0, -13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -18, 0,
    -18, -8, 0, 0, 0, 0, 0, -22,
    0, -11, 0, -2, 0, -2, -4, 0,
    0, -11, -2, 0, 0, 0, -5, 0,
    0, 0, 0, 0, 0, -7, 0, -13,
    0, 0, 0, 0, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -14,
    0, 0, 0, 0, -16, 0, 0, -12,
    -5, 0, -3, 0, 0, 0, 0, 0,
    -5, -2, 0, 0, -2, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 45,
    .right_class_cnt     = 38,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_source_han_sans_regular_30 = {
#else
lv_font_t font_source_han_sans_regular_30 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 35,          /*The maximum line height required by the font*/
    .base_line = 8,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -4,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_SOURCE_HAN_SANS_REGULAR_30*/

