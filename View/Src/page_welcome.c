//
// Created by <PERSON><PERSON> on 2024/7/2.
//

#include "page_welcome.h"
#include "comp_status_bar.h"

lv_obj_t *v_layout;

// 动画回调函数
static void anim_opacity_cb(void *obj, int32_t opa) {
    lv_obj_set_style_opa(obj, opa, 0);
}

// 淡出动画完成淡出回调函数
static void anim_fade_out_ready_cb(lv_anim_t *a) {
    // extern PageTypeHandle Page_Home;
    PageManager.main_page->state = page_valid;
    lv_obj_del(PageManager.main_page->obj);
    pm_page_start(&Pages.page[Page_Home]);
    // lv_group_focus_next(Pages.Page_Home.indev_group);
    // 只有从欢迎页进主页的时候才创建状态栏
    // 若已经被创建则删除掉再创建（RESET后）
    if (lv_obj_is_valid(status_bar)) {
        lv_obj_del(status_bar);
        status_bar = NULL;
    }
    status_bar = comp_status_bar_init();
    update_status_bar();

    Anim.mask = lv_obj_custom_trans_create(lv_layer_sys(), LV_PCT(100), LV_PCT(100));
    lv_obj_add_flag(Anim.mask, LV_OBJ_FLAG_HIDDEN);
}

// TODO 源页淡出目标页淡入作为动画类型加入框架
// 淡入动画完成后的回调函数
static void anim_ready_cb(lv_anim_t *a) {
    // 创建动画
    lv_anim_t anim_fade_out;
    lv_anim_init(&anim_fade_out);
    lv_anim_set_var(&anim_fade_out, v_layout);
    lv_anim_set_exec_cb(&anim_fade_out, anim_opacity_cb);
    lv_anim_set_time(&anim_fade_out, 1000);
    // 从透明到完全不透明
    lv_anim_set_values(&anim_fade_out, LV_OPA_COVER, LV_OPA_TRANSP);
    // 设置动画结束后的回调函数
    lv_anim_set_ready_cb(&anim_fade_out, anim_fade_out_ready_cb);
    lv_anim_start(&anim_fade_out);
}

void page_welcome_init(lv_obj_t *page) {
    // 创建垂直布局obj
    v_layout = lv_layout_custom_create(page, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_size(v_layout, LV_PCT(100), LV_SIZE_CONTENT);
    // 将垂直布局容器居中
    lv_obj_center(v_layout);
    lv_obj_set_style_bg_color(v_layout, lv_color_hex(0x000000), 0);
    lv_obj_add_flag(v_layout, LV_OBJ_FLAG_SCROLLABLE);
    // lv_obj_set_style_opa(v_layout,LV_OPA_50,0);

    // 设置布局属性
    // 设置子对象间的间距
    lv_obj_set_style_pad_gap(v_layout, 20, 0);
    // 将子对象居中
    lv_obj_set_flex_align(v_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);

    // 品牌logo
    lv_obj_t *logo = lv_img_custom_create(v_layout, &ico_logo);
    // 型号文字
    lv_obj_t *product_model_label = lv_label_custom_create(v_layout, Param.product_model, FONT_PRODUCT_MODEL,
                                                           lv_color_hex(0xA1D1FF), LV_TEXT_ALIGN_CENTER);

    // 创建动画
    lv_anim_t anim_fade_in;
    lv_anim_init(&anim_fade_in);
    lv_anim_set_var(&anim_fade_in, v_layout);
    lv_anim_set_exec_cb(&anim_fade_in, anim_opacity_cb);
    lv_anim_set_time(&anim_fade_in, 1200);
    // 从透明到完全不透明
    lv_anim_set_values(&anim_fade_in, LV_OPA_TRANSP, LV_OPA_COVER);
    // 设置动画结束后的回调函数
    lv_anim_set_ready_cb(&anim_fade_in, anim_ready_cb);
    lv_anim_start(&anim_fade_in);
}