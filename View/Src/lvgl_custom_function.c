//
// Created by <PERSON><PERSON> on 2024/7/8.
//

#include "lvgl_custom_function.h"
#include "page_manager.h"

// 创建布局obj
lv_obj_t *lv_layout_custom_create(lv_obj_t *parent, lv_flex_flow_t flow) {
    lv_obj_t *layout = lv_obj_create(parent);
    lv_obj_add_style(layout, &PageManager.default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    // 设置布局
    lv_obj_set_flex_flow(layout, flow);
    return layout;
}

// 创建文字
lv_obj_t *lv_label_custom_create(lv_obj_t *parent, char *str, const lv_font_t *font,
                                 lv_color_t color, lv_text_align_t align) {
    lv_obj_t *label = lv_label_create(parent);
    lv_label_set_text(label, str);
    lv_obj_set_style_text_color(label, color, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(label, font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(label, align, LV_PART_MAIN | LV_STATE_DEFAULT);
    return label;
}

// 创建图片
lv_obj_t *lv_img_custom_create(lv_obj_t *parent, const void *img_src) {
    // 创建图片部件
    lv_obj_t *img = lv_img_create(parent);
    // 设置图片源
    lv_img_set_src(img, img_src);
    return img;
}

// 设置背景颜色
void lv_obj_custom_set_bg_color(lv_obj_t *obj, lv_color_t color) {
    lv_obj_set_style_bg_color(obj, color, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(obj, LV_OPA_COVER, LV_PART_MAIN | LV_STATE_DEFAULT);
}

// 透明占位组件
lv_obj_t *lv_obj_custom_trans_create(lv_obj_t *parent, lv_coord_t width, lv_coord_t height) {
    lv_obj_t *trans_obj = lv_obj_create(parent);
    lv_obj_set_style_bg_opa(trans_obj, LV_OPA_TRANSP, 0);
    lv_obj_add_style(trans_obj, &PageManager.default_style, 0);
    lv_obj_set_size(trans_obj, width, height);
    return trans_obj;
}

// 画线 obj
lv_obj_t *lv_line_custom_creat(lv_obj_t *parent, lv_coord_t width, lv_coord_t height, lv_coord_t radius) {
    lv_obj_t *line = lv_obj_create(parent);
    lv_obj_add_style(line, &PageManager.default_style, 0);
    lv_obj_set_size(line, width, height);
    lv_obj_set_style_radius(line, radius, 0);
    return line;
}