//
// Created by <PERSON><PERSON> on 2024/7/31.
//

#include "page_grp_switch.h"
#include "comp_grp_switch.h"
#include "comp_bar.h"
#include "page_manager_anim_mode.h"

lv_obj_t *handle_grp_switch_left;

void page_grp_switch_init(lv_obj_t *page) {
    comp_group_list_home(page);
    // 左手柄视觉对象
    left_bar_init(page);
    handle_grp_switch_left = pm_create_handle(page, pos_left);
    // 删除状态栏，此页不允许拉状态栏
    // if (lv_obj_is_valid(status_bar)){
    //     lv_obj_del(status_bar);
    //     status_bar = NULL;
    // }
}