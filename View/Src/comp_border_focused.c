//
// Created by <PERSON><PERSON> on 2024/11/15.
//

#include "comp_border_focused.h"
#include "view_model.h"

bool last_event_is_enter_key = false;

// 在子对象获得焦点时，自动滚动Tile
void tile_auto_scroll_event_cb(lv_event_t *e) {
    // 避免enter_key那次的LV_EVENT_FOCUSED触发tile_auto_scroll_event，导致tile滚动异常
    if (last_event_is_enter_key) {
        last_event_is_enter_key = false;
        return;
    }
    lv_obj_t *target_obj = lv_event_get_target(e);
    // if (target_obj == test_obj_grp[group_A]) {
    //     LV_LOG("group_A\n");
    // } else if (target_obj == test_obj_grp[group_B]) {
    //     LV_LOG("group_B\n");
    // } else if (target_obj == test_obj_grp[group_C]) {
    //     LV_LOG("group_C\n");
    // } else if (target_obj == test_obj_grp[group_D]) {
    //     LV_LOG("group_D\n");
    // } else if (target_obj == test_obj_grp[group_E]) {
    //     LV_LOG("group_E\n");
    // } else {
    //     LV_LOG("unknown\n");
    // }
    GroupStruct *grp = lv_obj_get_user_data(target_obj->parent);
    if (grp == NULL) {
        return;
    }

    // 获取当前活跃的tile
    lv_tileview_tile_t *active_tile = (lv_tileview_tile_t *) lv_tileview_get_tile_act(Param.grp_tile_view);
    lv_tileview_tile_t *tile = grp->tile_gpr;
    if (active_tile != tile) {
        lv_obj_set_tile(Param.grp_tile_view, (lv_obj_t *) tile, LV_ANIM_ON);
    }
    lv_state_t state = lv_obj_get_state(target_obj);

    // lv_group_set_editing(lv_group_get_default(), false);后会再次触发LV_EVENT_FOCUSED事件，此时非LV_STATE_EDITED状态
    LV_LOG("state: %d\n", state);
    if (state & LV_STATE_EDITED) {
        LV_LOG("tile_auto_scroll_event_cb\n");
    }
}

// 在子对象获得焦点时，自动滚动父对象
void parent_auto_scroll_event_cb(lv_event_t *e) {
    lv_obj_t *target_obj = lv_event_get_target(e);
    lv_obj_t *parent_obj = target_obj->parent->parent;
    if (lv_obj_has_state(target_obj, LV_STATE_FOCUSED)) {
        // 获取子对象的实际坐标
        lv_area_t coords;
        lv_obj_get_coords(target_obj, &coords);

        lv_dir_t scroll_dir = lv_obj_get_scroll_dir(parent_obj);
        if (scroll_dir == LV_DIR_VER) {
            // 获取父对象的滚动偏移量
            lv_coord_t parent_scroll_top = lv_obj_get_scroll_top(parent_obj);
            // 获取父对象的高度
            lv_coord_t parent_height = lv_obj_get_height(parent_obj);
            lv_coord_t child_top = lv_obj_get_y(target_obj->parent);
            lv_coord_t child_height = lv_obj_get_height(target_obj->parent);
            // 子对象的实际 Y 坐标（coords.y1 表示左上角的 Y 坐标）
            lv_coord_t actual_child_top = coords.y1;
            // LV_LOG("child_top %d\n", child_top);
            // LV_LOG("parent_scroll_top %d\n", parent_scroll_top);
            // LV_LOG("parent_height %d\n", parent_height);

            if (parent_height && child_height) {
                uint8_t max_visible_items = parent_height / child_height;
                // 父对象可视区域 = 可见行数*(子对象高度+padding_gap)- padding_gap
                lv_coord_t parent_visible_height = max_visible_items * (child_height + 10) - 10;
                // LV_LOG("parent_height %d\n", parent_height);
                // LV_LOG("child_height %d\n", child_height);
                // LV_LOG("max_visible_items:%d\n", max_visible_items);
                // LV_LOG("child_top %d\n", child_top);
                // LV_LOG("parent_visible_height: %d\n", parent_visible_height);
                // LV_LOG("parent_scroll_top + parent_height: %d\n", parent_scroll_top + parent_height);
                // 如果子对象的位置超出了父对象的可视区域，滚动父对象
                if (child_top < parent_scroll_top) {
                    // LV_LOG("to top\n");
                    lv_obj_scroll_to(parent_obj, 0, child_top, LV_ANIM_ON);
                } else if (child_top > parent_scroll_top + parent_visible_height) {
                    // LV_LOG("to bottom\n");
                    // 父对象须比子对象的滚动位置少两个滑条的高度，才能保证最后一项在最底部
                    lv_obj_scroll_to(parent_obj, 0,
                                     child_top - (parent_visible_height + 10) / max_visible_items *
                                                 (max_visible_items - 1), LV_ANIM_ON);
                }
            }
        } else if (scroll_dir == LV_DIR_HOR) {
            // 获取父对象的滚动偏移量
            lv_coord_t parent_scroll_left = lv_obj_get_scroll_left(parent_obj);
            // 获取父对象的宽度
            lv_coord_t parent_height = lv_obj_get_width(parent_obj);
            lv_coord_t child_top = lv_obj_get_x(target_obj->parent);

            // 如果子对象的位置超出了父对象的可视区域，滚动父对象
            if (child_top < parent_scroll_left) {
                // LV_LOG("1\n");
                lv_obj_scroll_to(parent_obj, child_top, 0, LV_ANIM_ON);
            } else if (child_top > parent_scroll_left + parent_height) {
                // LV_LOG("2\n");
                // 10为距离状态栏的距离
                // 父对象须比子对象的滚动位置少两个格子的高度，才能保证最后一项在最底部
                lv_obj_scroll_to(parent_obj, child_top - (parent_height + 4) / 3 * 2, 0, LV_ANIM_ON);
            }
        }
    }
}

void focused_mode_key_cb(lv_event_t *e) {
    // if (lv_group_get_default() == Pages.Page_Home.indev_group){
    //     LV_LOG("Pages.Page_Home.indev_group\n");
    // } else if (lv_group_get_default() == indev_group){
    //     LV_LOG("indev_group\n");
    // }
    lv_event_code_t code = lv_event_get_code(e);

    // FocusedParamEnum user_data = (FocusedParamEnum) (uintptr_t) lv_event_get_user_data(e);
    // if (user_data & Focused_Editing_Mode) {
    //     opera_obj = lv_obj_get_child(target_obj->parent, 1);
    // } else {
    //     opera_obj = target_obj->parent;
    // }
    if (code == LV_EVENT_KEY) {
        lv_obj_t *target_obj = lv_event_get_target(e);
        lv_obj_t *opera_obj;
        uint8_t is_opera_parent = (uintptr_t) lv_event_get_user_data(e);
        // 如果没有子对象，则调用父对象事件
        if (is_opera_parent == 1) {
            opera_obj = target_obj->parent;
        } else {
            opera_obj = lv_obj_get_child(target_obj->parent, 1);
        }
        char c = *((char *) lv_event_get_param(e));
        LV_LOG("c %d\n", c);
        if (c == LV_KEY_ESC || c == LV_KEY_HOME || c == LV_KEY_END) {
            // if (lv_group_get_editing(lv_group_get_default())) {
            lv_event_send(opera_obj, LV_EVENT_KEY, &c);
            // Pages.is_long_pressing = true;
            // } else {
            //     Pages.key_long_press++;
            //     if (Pages.key_long_press > 10 && !Pages.is_long_pressing) {
            //         c = LV_KEY_HOME;
            //         lv_event_send(opera_obj, LV_EVENT_KEY, &c);
            //         Pages.is_long_pressing = true;
            //     }
            // lv_event_send(opera_obj, LV_EVENT_KEY, &c);
            // Pages.is_long_pressing = true;
            // }
        } else if (c == LV_KEY_BACKSPACE) {
            reset_key_long_press_state();
        }
    }
    // if (code == LV_EVENT_DEFOCUSED) {
    //     LV_LOG("1233\n");
    // }
    // LV_LOG("%d\n", lv_obj_get_state(target_obj));
}

void border_key_event_cb(lv_event_t *e) {
    lv_obj_t *target_obj = lv_event_get_target(e);
    lv_obj_t *opera_obj = lv_obj_get_child(target_obj->parent, 1);
    uint8_t is_opera_parent = (uintptr_t) lv_event_get_user_data(e);
    // 如果没有子对象，则调用父对象事件
    if (is_opera_parent == 1) {
        opera_obj = target_obj->parent;
    }

    // uintptr_t has_editing_mode = (uintptr_t) lv_event_get_user_data(e);
    char c = *((char *) lv_event_get_param(e));
    // LV_LOG("c %d\n", c);
    if (c == LV_KEY_ENTER) {
        // 单击模拟长按去除编辑状态
        // lv_obj_clear_state(target_obj, LV_STATE_EDITED);
        // 源码在static void indev_encoder_proc(lv_indev_t * i, lv_indev_data_t * data)函数中的
        // lv_group_set_editing(g, lv_group_get_editing(g) ? false : true); /*Toggle edit mode on long press*/处理
        if (lv_group_get_default() == Pages.page[Page_Home].indev_group) {
            LV_LOG("---------------Pages.page[Page_Home].indev_group\n");
        } else if (lv_group_get_default() == Pages.page[Page_Multi].indev_group) {
            LV_LOG("---------------Pages.page[Page_Multi].indev_group\n");
        } else {
            LV_LOG("---------------unknow\n");
        }
        lv_group_set_editing(lv_group_get_default(), false);
        // lv_group_focus_obj(NULL);
    } else if (c == LV_KEY_LEFT || c == LV_KEY_RIGHT) {
        lv_event_send(opera_obj, LV_EVENT_KEY, &c);
    }
}

void enter_key_event_cb(lv_event_t *e) {
    lv_obj_t *target_obj = lv_event_get_target(e);
    lv_state_t state = lv_obj_get_state(target_obj);
    last_event_is_enter_key = false;

    // lv_group_set_editing(lv_group_get_default(), false);后会再次触发LV_EVENT_FOCUSED事件，此时非LV_STATE_EDITED状态
    if (state & LV_STATE_EDITED) {
        LV_LOG("LV_STATE_EDITED\n");
        // lv_obj_t *opera_obj = lv_obj_get_child(target_obj->parent, 0);
        lv_group_set_editing(lv_group_get_default(), false);
        char c = LV_KEY_ENTER;
        if (lv_obj_is_valid(target_obj->parent)) {
            lv_event_send(target_obj->parent, LV_EVENT_KEY, &c);
        }
        last_event_is_enter_key = true;
    }
}

// 滚轮聚焦边框
lv_obj_t *border_focused_obj_init(lv_obj_t *parent, PageTypeHandle *page, FocusedParamEnum FocusedParam) {
    // 聚焦时的边框
    lv_obj_t *border_obj = lv_obj_custom_trans_create(parent, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_radius(border_obj, 32, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_move_foreground(border_obj);
    lv_obj_add_flag(border_obj, LV_OBJ_FLAG_IGNORE_LAYOUT);
    lv_obj_set_style_border_width(border_obj, 3, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_color(border_obj, lv_color_white(), LV_PART_MAIN | LV_STATE_FOCUSED);
    uint8_t opr_parent_flag = 0;
    if (FocusedParam & Focused_Opr_Parent) {
        // lv_obj_update_layout(parent);
        lv_coord_t parent_radius = lv_obj_get_style_radius(parent, LV_PART_MAIN);
        if (!parent_radius) {
            parent_radius = 32;
        }
        lv_obj_set_style_radius(border_obj, parent_radius, LV_PART_MAIN | LV_STATE_DEFAULT);
        opr_parent_flag = 1;
    }
    if (FocusedParam & Focused_Editing_Mode) {
        lv_obj_set_style_border_color(border_obj, lv_color_hex(TTL_Color), LV_PART_MAIN | LV_STATE_EDITED);
        lv_obj_add_event_cb(border_obj, border_key_event_cb, LV_EVENT_KEY, (void *) (uintptr_t) opr_parent_flag);
    } else {
        // 没有editing_mode的组件模拟Enter_key
        lv_obj_add_event_cb(border_obj, enter_key_event_cb, LV_EVENT_FOCUSED, NULL);
    }
    if (FocusedParam & Focused_Scroll_Parent) {
        lv_obj_add_event_cb(border_obj, parent_auto_scroll_event_cb, LV_EVENT_FOCUSED, NULL);
    } else if (FocusedParam & Focused_Scroll_Tile) {
        lv_obj_add_event_cb(border_obj, tile_auto_scroll_event_cb, LV_EVENT_FOCUSED, NULL);
    }
    lv_obj_add_event_cb(border_obj, focused_mode_key_cb, LV_EVENT_ALL, (void *) (uintptr_t) opr_parent_flag);

    lv_obj_clear_flag(border_obj, LV_OBJ_FLAG_CLICKABLE);

    // 将边框绘制在背景之前
    // lv_obj_set_style_border_post(slider_comp, false, 0);

    // 输入组未创建则创建
    if (page->indev_group == NULL) {
        page->indev_group = lv_group_create();
        if (page->indev_group == Pages.page[Page_Home].indev_group) {
            LV_LOG("---------------Pages.page[Page_Home].indev_group\n");
        } else if (page->indev_group == Pages.page[Page_Multi].indev_group) {
            LV_LOG("---------------Pages.page[Page_Multi].indev_group\n");
        } else {
            LV_LOG("---------------unknown\n");
        }
    }
    lv_group_add_obj(page->indev_group, border_obj);
    // 将添加进group的obj指针保存下来
    for (int i = 0; i < GROUP_OBJ_MAX; ++i) {
        if (page->group_obj[i] == NULL) {
            page->group_obj[i] = border_obj;
            break;
        }
    }
    return border_obj;
}