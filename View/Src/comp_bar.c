//
// Created by <PERSON><PERSON> on 2024/8/1.
//

#include "comp_bar.h"
#include "page_manager.h"

void left_bar_init(lv_obj_t *parent) {
    lv_obj_t *bar = lv_obj_create(parent);
    lv_obj_add_style(bar, &PageManager.default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_size(bar, 4, 86);
    lv_obj_align(bar, LV_ALIGN_LEFT_MID, 6, 0);
    lv_obj_set_style_radius(bar, 10, 0);
    lv_obj_clear_flag(bar, LV_OBJ_FLAG_CLICKABLE);
}

void bottom_bar_init(lv_obj_t *parent) {
    lv_obj_t *bar = lv_obj_create(parent);
    lv_obj_add_style(bar, &PageManager.default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_size(bar, 86, 4);
    lv_obj_align(bar, LV_ALIGN_BOTTOM_MID, 0, -10);
    lv_obj_set_style_radius(bar, 2, 0);
    lv_obj_clear_flag(bar, LV_OBJ_FLAG_CLICKABLE);
}