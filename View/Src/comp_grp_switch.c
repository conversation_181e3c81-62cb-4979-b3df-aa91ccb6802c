//
// Created by <PERSON><PERSON> on 2024/8/1.
//

#include "comp_grp_switch.h"
#include "user.h"
#include "comp_status_bar.h"
#include "comp_border_focused.h"
#include "view_model.h"
#include "utils.h"

uint32_t grp_color_arr[] = {COLOR_GROUP_A, COLOR_GROUP_B, COLOR_GROUP_C, COLOR_GROUP_D, COLOR_GROUP_E};
lv_obj_t *group_switch_arr[GroupItemCount];

lv_obj_t *comp_color_point_init(lv_obj_t *parent, GroupNameEnum group_name) {
    lv_obj_t *point = lv_obj_create(parent);
    lv_obj_add_style(point, &PageManager.default_style, 0);
    lv_obj_set_size(point, 12, 12);
    lv_obj_set_style_radius(point, 6, 0);
    if (Param.is_grp_color_on) {
        lv_obj_set_style_bg_color(point, lv_color_hex(grp_color_arr[group_name]), 0);
    } else {
        lv_obj_set_style_bg_color(point, lv_color_white(), 0);
    }
    return point;
}

void check_switch_state(GroupStruct *group, lv_obj_t *group_switch, lv_obj_t *point, lv_obj_t *label,
                        SwitchEnumType switch_type) {
    bool check_value;
    if (switch_type == switch_home) {
        check_value = group->is_added_to_list;
    } else {
        check_value = group->is_turn_on_Multi;
    }
    if (check_value) {
        // 开
        lv_obj_set_style_bg_color(group_switch, lv_color_hex(Bg_Color_Gray), 0);
        if (switch_type == switch_home) {
            lv_obj_clear_flag(point, LV_OBJ_FLAG_HIDDEN);
            if (Param.is_grp_color_on) {
                lv_obj_set_style_bg_color(point, lv_color_hex(grp_color_arr[group->group_name]), 0);
            } else {
                lv_obj_set_style_bg_color(point, lv_color_white(), 0);
            }
        } else {
            if (Param.is_grp_color_on) {
                lv_obj_set_style_bg_color(point, lv_color_hex(grp_color_arr[group->group_name]), 0);
            } else {
                lv_obj_add_flag(point, LV_OBJ_FLAG_HIDDEN);
            }
        }
        lv_obj_set_style_text_color(label, lv_color_white(), 0);
    } else {
        // 关
        lv_obj_set_style_bg_color(group_switch, lv_color_hex(Bg_Color_Black), 0);
        if (switch_type == switch_home) {
            lv_obj_clear_flag(point, LV_OBJ_FLAG_HIDDEN);
            lv_obj_set_style_bg_color(point, lv_color_hex(Unselected_Label_Color), 0);
        } else {
            if (Param.is_grp_color_on) {
                lv_obj_set_style_bg_color(point, lv_color_hex(Unselected_Label_Color), 0);
            } else {
                lv_obj_add_flag(point, LV_OBJ_FLAG_HIDDEN);
            }
        }
        lv_obj_set_style_text_color(label, lv_color_hex(Unselected_Label_Color), 0);
    }
}

// 组别开关事件处理
void grp_switch_event_handler(lv_event_t *e) {
    lv_obj_t *group_switch = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    SwitchStructType *switch_struct = lv_obj_get_user_data(group_switch);

    if (code == LV_EVENT_CLICKED || code == LV_EVENT_KEY) {
        char c = 0;
        if (code == LV_EVENT_KEY) {
            c = *((char *) lv_event_get_param(e));
            if (switch_struct->switch_type == switch_multi) {
                if (c == LV_KEY_ESC) {
                    // 创建页面
                    pm_creat_page(PageManager.main_page->pos[pos_right].page, PageManager.main_page,
                                  LV_ALIGN_OUT_RIGHT_MID,
                                  lv_color_black());
                    page_click_anim(pos_right, &Pages.page[Page_Home], anim_slide);
                } else if (c == LV_KEY_END) {
                    pm_click_to_page_control_center();
                }
            } else if (switch_struct->switch_type == switch_home) {
                if (c == LV_KEY_HOME) {
                    // 创建页面
                    pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page,
                                  LV_ALIGN_OUT_LEFT_MID,
                                  lv_color_black());
                    page_click_anim(pos_left, &Pages.page[Page_Home], anim_slide);
                }
            }
        }
        if (c == LV_KEY_ENTER || code == LV_EVENT_CLICKED) {
            if (switch_struct->switch_type == switch_multi) {
                switch_struct->group->is_turn_on_Multi = !switch_struct->group->is_turn_on_Multi;
            } else {
                switch_struct->group->is_added_to_list = !switch_struct->group->is_added_to_list;
            }
            lv_event_send(group_switch, LV_EVENT_VALUE_CHANGED, NULL);
        }
    } else if (code == LV_EVENT_VALUE_CHANGED) {
        lv_obj_t *label = lv_obj_get_child(group_switch, 0);
        lv_obj_t *point = lv_obj_get_child(group_switch, 1);
        check_switch_state(switch_struct->group, group_switch, point, label, switch_struct->switch_type);
        if (switch_struct->switch_type == switch_home) {
            set_flash_mode_level();
        } else {
            set_multi_param();
        }
    }
}

// 组别组件
lv_obj_t *comp_group_switch(lv_obj_t *parent, GroupStruct *group, SwitchEnumType switch_type) {
    uint8_t comp_height, comp_width;
    // 注意：padding会挤压size的大小
    if (switch_type == switch_home) {
        comp_height = 114;
        comp_width = 104;
    } else {
        comp_height = 90;
        comp_width = 108;
    }

    lv_obj_t *group_switch = lv_layout_custom_create(parent, LV_FLEX_FLOW_COLUMN);
    // lv_obj_set_style_pad_top(group_switch, 12, 0);
    lv_obj_set_size(group_switch, comp_width, comp_height);
    lv_obj_set_style_bg_color(group_switch, lv_color_hex(Bg_Color_Gray), 0);
    lv_obj_set_style_radius(group_switch, RADIUS_DEFAULT, 0);
    lv_obj_set_flex_align(group_switch, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_pad_gap(group_switch, POINT_GAP, 0);
    lv_obj_add_event_cb(group_switch, grp_switch_event_handler, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(group_switch, free_struct_cb, LV_EVENT_DELETE, NULL);
    // 组别开关结构体，用户数据
    SwitchStructType *switch_struct = (SwitchStructType *) malloc(sizeof(SwitchStructType));
    if (switch_struct == NULL) {
        // 如果分配失败，处理错误
        LV_LOG("switch_struct Memory allocation failed\n");
        return NULL;
    }
    switch_struct->switch_type = switch_type;
    switch_struct->group = group;
    lv_obj_set_user_data(group_switch, switch_struct);

    // 占位obj
    // lv_obj_t *trans_obj = lv_obj_custom_trans_create(group_switch, comp_width, 0);
    //
    // lv_obj_t *line = lv_obj_create(group_switch);
    // lv_obj_add_style(line, &PageManager.default_style, 0);
    // lv_obj_set_style_bg_color(line, lv_color_hex(Main_Color), 0);
    // lv_obj_set_size(line, 50, line_height);
    // lv_obj_set_style_radius(line, 3, 0);

    lv_obj_t *label = lv_label_custom_create(group_switch, group_text[group->group_name], FONT_HOME_GRP,
                                             lv_color_white(), LV_TEXT_ALIGN_CENTER);
    lv_obj_t *point = comp_color_point_init(group_switch, group->group_name);
    check_switch_state(group, group_switch, point, label, switch_type);

    if (switch_type == switch_home) {
        group->home_switch = group_switch;
        // lv_obj_set_style_pad_gap(group_switch, 15, 0);
        border_focused_obj_init(group_switch, &Pages.page[Page_Group_Switch], Focused_Opr_Parent);
    } else if (switch_type == switch_multi) {
        group->multi_switch = group_switch;
        border_focused_obj_init(group_switch, &Pages.page[Page_Multi], Focused_Opr_Parent | Focused_Scroll_Parent);
    }
    return group_switch;
}

void comp_group_list_multi(lv_obj_t *parent) {
    lv_obj_t *h_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW);
    lv_obj_set_scrollbar_mode(h_layout, LV_SCROLLBAR_MODE_AUTO);
    lv_obj_set_style_bg_opa(h_layout, LV_OPA_TRANSP, 0);
    uint8_t gap_pad_top = 6;
    uint8_t gap_pad_bottom = 16;
    // 注意：padding会挤压size的大小
    uint8_t comp_height = 90;
    uint8_t pad_gap = 4;
    lv_obj_set_size(h_layout, 108 * 3 + pad_gap * 2, comp_height + gap_pad_top + gap_pad_bottom);
    lv_obj_set_style_pad_top(h_layout, gap_pad_top, 0);
    // lv_obj_set_style_pad_hor(h_layout, 15, 0);
    lv_obj_set_style_pad_gap(h_layout, pad_gap, 0);
    // 设置滚动方向，便于焦点判断
    lv_obj_set_scroll_dir(h_layout, LV_DIR_HOR);

    for (int i = 0; i < GroupItemCount; ++i) {
        comp_group_switch(h_layout, &Param.group[i], switch_multi);
    }
    // comp_group_switch(h_layout, &Param.group_A, switch_multi);
    // comp_group_switch(h_layout, &Param.group_B, switch_multi);
    // comp_group_switch(h_layout, &Param.group_C, switch_multi);
    // comp_group_switch(h_layout, &Param.group_D, switch_multi);
    // comp_group_switch(h_layout, &Param.group_E, switch_multi);
}

// 颜色选择事件处理函数
static void color_select_event_handler(lv_event_t *e) {
    lv_obj_t *obj = lv_event_get_target(e);
    lv_obj_t *img = lv_event_get_user_data(e);
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED ||
        (code == LV_EVENT_KEY && *((char *) lv_event_get_param(e)) == LV_KEY_ENTER)) {
        // 切换状态
        Param.is_grp_color_on = !Param.is_grp_color_on;

        // 更新背景色
        lv_obj_set_style_bg_color(obj,
                                  Param.is_grp_color_on ? lv_color_hex(Bg_Color_Gray) : lv_color_hex(Bg_Color_Black),
                                  0);

        // 更新图标
        lv_img_set_src(img, Param.is_grp_color_on ? &icon_grp_color_unselected : &icon_grp_color_selected);
        for (int i = 0; i < GroupItemCount; ++i) {
            lv_event_send(group_switch_arr[i], LV_EVENT_VALUE_CHANGED, NULL);
        }
    } else if (code == LV_EVENT_KEY && *((char *) lv_event_get_param(e)) == LV_KEY_HOME) {
        // 创建页面
        pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page,
                      LV_ALIGN_OUT_LEFT_MID,
                      lv_color_black());
        page_click_anim(pos_left, &Pages.page[Page_Home], anim_slide);
    }
}

void comp_group_list_home(lv_obj_t *parent) {
    lv_obj_t *h_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_scrollbar_mode(h_layout, LV_SCROLLBAR_MODE_OFF);
    lv_obj_set_style_bg_opa(h_layout, LV_OPA_TRANSP, 0);
    uint8_t pad_top = status_bar_h + 10;
    // 注意：padding会挤压size的大小
    lv_obj_set_size(h_layout, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_pad_top(h_layout, pad_top, 0);
    lv_obj_set_style_pad_hor(h_layout, 18, 0);
    lv_obj_set_style_pad_gap(h_layout, 10, 0);

    for (int i = 0; i < GroupItemCount; ++i) {
        group_switch_arr[i] = comp_group_switch(h_layout, &Param.group[i], switch_home);
    }

    lv_obj_t *color_select_obj = lv_obj_create(h_layout);
    lv_obj_add_style(color_select_obj, &PageManager.default_style, 0);
    lv_obj_add_flag(color_select_obj, LV_OBJ_FLAG_IGNORE_LAYOUT);
    lv_obj_set_size(color_select_obj, LV_PCT(100), 114);
    lv_obj_set_style_radius(color_select_obj, RADIUS_DEFAULT, 0);

    // 根据初始状态设置背景色和图标
    if (Param.is_grp_color_on) {
        lv_obj_set_style_bg_color(color_select_obj, lv_color_hex(Bg_Color_Gray), 0);
    } else {
        lv_obj_set_style_bg_color(color_select_obj, lv_color_hex(Bg_Color_Black), 0);
    }
    lv_obj_align(color_select_obj, LV_ALIGN_BOTTOM_MID, 0, -30);

    lv_obj_t *img_color = lv_img_custom_create(color_select_obj,
                                               Param.is_grp_color_on ? &icon_grp_color_unselected
                                                                     : &icon_grp_color_selected);
    lv_obj_center(img_color);

    // 添加点击事件回调
    lv_obj_add_event_cb(color_select_obj, color_select_event_handler, LV_EVENT_ALL, img_color);

    border_focused_obj_init(color_select_obj, &Pages.page[Page_Group_Switch], Focused_Opr_Parent);
}
