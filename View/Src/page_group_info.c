//
// Created by <PERSON><PERSON> on 2024/6/15.
//

#include "page_group_info.h"
#include "comp_status_bar.h"
#include "comp_slider.h"
#include "comp_bar.h"
#include "comp_border_focused.h"
#include "view_model.h"
#include "utils.h"
#include "comp_grp_switch.h"

lv_obj_t *handle_group_info_left;
lv_obj_t *handle_group_info_top;

// lv_obj_t *test_obj_grp[5];

// 模式区域事件处理
static void mode_event_handler(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_obj_t *label = lv_obj_get_child(tar_obj, 0);
    lv_event_code_t code = lv_event_get_code(e);
    GroupStruct *grp = lv_obj_get_user_data(tar_obj);
    if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_HOME) {
                pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page, LV_ALIGN_OUT_LEFT_MID,
                              lv_color_black());
                page_click_anim(pos_left, &Pages.page[Page_Home], anim_slide);
            } else if (c == LV_KEY_END) {
                pm_click_to_page_control_center();
            }
            if (c != LV_KEY_ENTER && c != LV_KEY_ESC) {
                return;
            }
        }
        mode_switch_handle(grp);
        lv_event_send(tar_obj, LV_EVENT_VALUE_CHANGED, NULL);
        // lv_event_send(grp->slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
        lv_event_send(grp->slider_option_flash_level.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
    } else if (code == LV_EVENT_VALUE_CHANGED) {
        LV_LOG("change\n");
        lv_label_set_text(label, mode_text[grp->mode]);
        mode_label_color_handle(label, grp->mode);
        if (grp->mode == mode_OFF) {
            lv_obj_add_flag(grp->slider_grp_layout, LV_OBJ_FLAG_HIDDEN);
        } else {
            lv_obj_clear_flag(grp->slider_grp_layout, LV_OBJ_FLAG_HIDDEN);
        }
        set_flash_mode_level();
    }
}

// 组别区域事件处理
static void grp_event_handler(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    // lv_obj_t *current_tile = lv_tileview_get_tile_act(tileview);
    lv_event_code_t code = lv_event_get_code(e);
    GroupStruct *grp = lv_obj_get_user_data(tar_obj);
    // GroupStruct *groups[] = {
    //         &Param.group_A,
    //         &Param.group_B,
    //         &Param.group_C,
    //         &Param.group_D,
    //         &Param.group_E
    // };
    // size_t num_groups = sizeof(groups) / sizeof(groups[0]);
    lv_tileview_tile_t *tile = grp->tile_gpr;
    if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_HOME) {
                pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page, LV_ALIGN_OUT_LEFT_MID,
                              lv_color_black());
                page_click_anim(pos_left, &Pages.page[Page_Home], anim_slide);
            } else if (c == LV_KEY_END) {
                pm_click_to_page_control_center();
            } else if (c == LV_KEY_ESC) {
                // mode键切换模式
                mode_switch_handle(grp);
                lv_event_send(grp->mode_layout, LV_EVENT_VALUE_CHANGED, NULL);
                lv_event_send(grp->slider_option_flash_level.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
            }
            if (c != LV_KEY_ENTER) {
                return;
            }
        }
        lv_obj_t *child_obj = lv_obj_get_child(tar_obj, 1);
        lv_state_t state = lv_obj_get_state(child_obj);
        // GroupNameEnum grp_name = grp->group_name;

        uint8_t grp_turn_on[GroupItemCount];
        uint8_t grp_arr_index = 0;
        for (GroupNameEnum i = 0; i < GroupItemCount; ++i) {
            if (Param.group[i].is_added_to_list) {
                grp_turn_on[grp_arr_index] = i;
                grp_arr_index++;
            }
        }

        // 下一个组的名字
        GroupNameEnum next_grp_name;
        // 查找当前组别在已打开列表的下标
        uint8_t now_index = binary_search_index(grp_turn_on, grp_arr_index, grp->group_name);
        // 如果是最后一个则跳回第一个
        if (now_index == grp_arr_index - 1) {
            next_grp_name = grp_turn_on[0];
        } else {
            next_grp_name = grp_turn_on[now_index + 1];
        }
        // 切换tile
        lv_obj_set_tile(Param.grp_tile_view, (lv_obj_t *) Param.group[next_grp_name].tile_gpr,
                        LV_ANIM_OFF);
        LV_LOG("now_index %d, state %d\n", now_index, state);
        // 若是聚焦状态则切换后也聚焦
        if (state & LV_STATE_FOCUSED) {
            lv_tileview_tile_t *act_tile = Param.group[next_grp_name].tile_gpr;
            lv_obj_t *h_layout = lv_obj_get_child(lv_obj_get_child((lv_obj_t *) act_tile, 0), 0);
            lv_obj_t *focused_obj_parent = lv_obj_get_child(h_layout, 0);
            lv_obj_t *focused_obj = lv_obj_get_child(focused_obj_parent, 1);
            lv_group_focus_obj(focused_obj);
        }
    }
}

// 每组的tile初始化
void page_group_tile_init(lv_tileview_tile_t *tile, GroupStruct *group) {
    char *grp_str = group_text[group->group_name];
    char *mode_str = mode_text[group->mode];
    lv_obj_t *v_layout = lv_layout_custom_create((lv_obj_t *) tile, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_size(v_layout, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_pad_hor(v_layout, 18, 0);
    lv_obj_set_style_bg_opa(v_layout, LV_OPA_TRANSP, 0);

    // 组别模式布局
    lv_obj_t *h_layout = lv_layout_custom_create(v_layout, LV_FLEX_FLOW_ROW);
    lv_obj_set_size(h_layout, LV_PCT(100), 84);
    lv_obj_set_style_radius(h_layout, RADIUS_DEFAULT, 0);
    lv_obj_set_style_pad_gap(h_layout, 0, 0);
    lv_obj_set_style_bg_color(h_layout, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_set_style_bg_opa(h_layout, LV_OPA_COVER, 0);
    lv_obj_set_style_clip_corner(h_layout, true, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t *grp_layout = lv_layout_custom_create(h_layout, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_style_radius(grp_layout, RADIUS_DEFAULT, 0);
    lv_obj_set_size(grp_layout, LV_PCT(50), LV_PCT(100));
    lv_obj_set_style_bg_opa(grp_layout, LV_OPA_COVER, 0);
    lv_obj_set_style_bg_color(grp_layout, lv_color_hex(Bg_Color_Gray), 0);
    lv_obj_set_style_pad_gap(grp_layout, 0, 0);

    lv_obj_t *grp_label = lv_label_custom_create(grp_layout, grp_str, FONT_GRP_MODE, lv_color_white(),
                                                 LV_TEXT_ALIGN_CENTER);
    lv_obj_set_flex_align(grp_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_event_cb(grp_layout, grp_event_handler, LV_EVENT_ALL, NULL);
    lv_obj_set_user_data(grp_layout, group);

    border_focused_obj_init(grp_layout, &Pages.page[Page_Group_Info],
                            Focused_Scroll_Tile | Focused_Opr_Parent);

    lv_obj_t *grp_color_point = comp_color_point_init(grp_layout, group->group_name);
    if (Param.is_grp_color_on) {
        lv_obj_clear_flag(grp_color_point, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(grp_color_point, LV_OBJ_FLAG_HIDDEN);
    }

    // test_obj_grp[group->group_name] = test1;
    // lv_obj_set_style_border_width(grp_layout, 1, 0);

    lv_obj_t *mode_layout = lv_layout_custom_create(h_layout, LV_FLEX_FLOW_ROW);
    lv_obj_set_size(mode_layout, LV_PCT(50), LV_PCT(100));
    lv_obj_set_style_bg_opa(mode_layout, LV_OPA_TRANSP, 0);
    lv_obj_t *mode_label = lv_label_custom_create(mode_layout, mode_str, FONT_GRP_MODE, lv_color_white(),
                                                  LV_TEXT_ALIGN_CENTER);
    group->mode_layout = mode_layout;
    if (group->mode == mode_TTL) {
        lv_obj_set_style_text_color(mode_label, lv_color_hex(TTL_Color), 0);
    } else if (group->mode == mode_OFF) {
        lv_obj_set_style_text_color(mode_label, lv_color_hex(OFF_Color), 0);
    }
    lv_obj_set_flex_align(mode_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_event_cb(mode_layout, mode_event_handler, LV_EVENT_ALL, NULL);
    lv_obj_set_user_data(mode_layout, group);
    border_focused_obj_init(mode_layout, &Pages.page[Page_Group_Info], Focused_Opr_Parent);

    // 滑动条布局
    lv_obj_t *slider_layout = slider_layout_grp_page(v_layout, group);

    // 底部指示器布局
    lv_obj_t *bottom_icon_layout = lv_layout_custom_create(v_layout, LV_FLEX_FLOW_ROW);
    lv_obj_add_flag(bottom_icon_layout, LV_OBJ_FLAG_IGNORE_LAYOUT);
    lv_obj_set_size(bottom_icon_layout, LV_PCT(100), LV_SIZE_CONTENT);
    lv_obj_set_style_bg_opa(bottom_icon_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_style_pad_gap(bottom_icon_layout, 0, 0);
    // 对齐父对象效果会异常
    lv_obj_align_to(bottom_icon_layout, h_layout, LV_ALIGN_OUT_BOTTOM_MID, 0, opt_slider_layout_height + 12 + 10);

    lv_obj_set_flex_align(bottom_icon_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    group->icon_tile_up = lv_img_custom_create(bottom_icon_layout, &icon_up_20);
    lv_obj_t *icon_mid = lv_img_custom_create(bottom_icon_layout, &icon_mid_20);
    group->icon_tile_down = lv_img_custom_create(bottom_icon_layout, &icon_down_20);
}

// GroupStruct *find_first_and_last_added_groups(ParamStruct *param, GroupStruct **first,
//                                                   GroupStruct **last) {
//     GroupStruct *groups[] = {
//             &param->group_A,
//             &param->group_B,
//             &param->group_C,
//             &param->group_D,
//             &param->group_E
//     };
//     size_t num_groups = sizeof(groups) / sizeof(groups[0]);
//
//     *first = NULL;
//     *last = NULL;
//
//     for (size_t i = 0; i < num_groups; i++) {
//         if (groups[i]->is_added_to_list) {
//             if (*first == NULL) {
//                 *first = groups[i];
//             }
//             *last = groups[i];
//         }
//     }
//
//     return *first; // 返回第一个找到的 group，如果没有找到则返回 NULL
// }

// typedef struct {
//     GroupStruct *group;
//     lv_obj_t *tile;
// } GroupTilePair;
//
// void update_group_tile_visibility(GroupTilePair *pair) {
//     if (pair->group->is_added_to_list) {
//         lv_obj_add_flag(pair->tile, LV_OBJ_FLAG_HIDDEN);
//     } else {
//         lv_obj_clear_flag(pair->tile, LV_OBJ_FLAG_HIDDEN);
//     }
// }
//
// void update_all_group_tiles() {
//     GroupTilePair pairs[] = {
//             {&Param.group_A, (lv_obj_t *) GrpTile.tile_gpr_A},
//             {&Param.group_B, (lv_obj_t *) GrpTile.tile_gpr_B},
//             {&Param.group_C, (lv_obj_t *) GrpTile.tile_gpr_C},
//             {&Param.group_D, (lv_obj_t *) GrpTile.tile_gpr_D},
//             {&Param.group_E, (lv_obj_t *) GrpTile.tile_gpr_E},
//     };
//
//     size_t num_pairs = sizeof(pairs) / sizeof(pairs[0]);
//
//     for (size_t i = 0; i < num_pairs; i++) {
//         update_group_tile_visibility(&pairs[i]);
//     }
// }

void page_group_info_init(lv_obj_t *page) {
    Param.grp_tile_view = lv_tileview_create(page);
    lv_obj_set_size(Param.grp_tile_view, LV_PCT(100), Screen_Height);
    lv_obj_align(Param.grp_tile_view, LV_ALIGN_TOP_MID, 0, status_bar_h + 10);
    lv_obj_set_style_bg_opa(Param.grp_tile_view, LV_OPA_TRANSP, 0);
    // 滚动时显示滚动条
    // lv_obj_set_scrollbar_mode(grp_tile_view, LV_SCROLLBAR_MODE_ACTIVE);
    lv_obj_set_scrollbar_mode(Param.grp_tile_view, LV_SCROLLBAR_MODE_OFF);
    // // 设置滚动条样式
    // static lv_style_t style_scrollbar;
    // lv_style_init(&style_scrollbar);
    // lv_style_set_bg_color(&style_scrollbar, lv_color_hex(0xFFFFFF));
    // lv_style_set_bg_opa(&style_scrollbar, LV_OPA_70);
    // // 设置圆角
    // lv_style_set_radius(&style_scrollbar, 5);
    // // 为容器的滚动条添加样式
    // lv_obj_add_style(grp_tile_view, &style_scrollbar, LV_PART_SCROLLBAR);

    // GroupStruct *groups[] = {
    //         &Param.group_A,
    //         &Param.group_B,
    //         &Param.group_C,
    //         &Param.group_D,
    //         &Param.group_E
    // };
    // size_t num_groups = sizeof(groups) / sizeof(groups[0]);

    uint8_t j = 0;
    // 用一个数组存储有效的组别
    uint8_t valid_group[GroupItemCount];
    memset(valid_group, 0xFF, sizeof(valid_group));
    uint8_t valid_group_index = 0;
    for (size_t i = 0; i < GroupItemCount; i++) {
        if (Param.group[i].is_added_to_list) {
            LV_LOG("j: %d\n", j);
            Param.group[i].tile_gpr = (lv_tileview_tile_t *) lv_tileview_add_tile(Param.grp_tile_view, 0, j,
                                                                                  LV_DIR_VER);
            page_group_tile_init(Param.group[i].tile_gpr, &Param.group[i]);
            valid_group[valid_group_index] = i;
            valid_group_index++;
            j++;
        }
    }

    uint8_t last_index = 0xFF;
    for (int i = 0; i < GroupItemCount; i++) {
        if (valid_group[i] != 0xFF) {
            last_index = i;
        } else {
            break;
        }
    }

    if (last_index != 0xFF) {
        lv_obj_set_style_img_opa(Param.group[valid_group[0]].icon_tile_up, LV_OPA_TRANSP, 0);
        lv_obj_set_style_img_opa(Param.group[valid_group[last_index]].icon_tile_down, LV_OPA_TRANSP, 0);
    }

    // GrpTile.tile_gpr_E = groups[4]->tile_gpr;

    // lv_tileview_tile_t *tile_gpr_A = (lv_tileview_tile_t *) lv_tileview_add_tile(grp_tile_view, 0, 0, LV_DIR_BOTTOM);
    // GrpTile.tile_gpr_A = (lv_tileview_tile_t *) lv_tileview_add_tile(GrpTile.grp_tile_view, 0, 0, LV_DIR_VER);
    // GrpTile.tile_gpr_B = (lv_tileview_tile_t *) lv_tileview_add_tile(GrpTile.grp_tile_view, 0, 1, LV_DIR_VER);
    // GrpTile.tile_gpr_C = (lv_tileview_tile_t *) lv_tileview_add_tile(GrpTile.grp_tile_view, 0, 2, LV_DIR_VER);
    // GrpTile.tile_gpr_D = (lv_tileview_tile_t *) lv_tileview_add_tile(GrpTile.grp_tile_view, 0, 3, LV_DIR_VER);
    // GrpTile.tile_gpr_E = (lv_tileview_tile_t *) lv_tileview_add_tile(GrpTile.grp_tile_view, 0, 4, LV_DIR_VER);
    // lv_tileview_tile_t *tile_gpr_E = (lv_tileview_tile_t *) lv_tileview_add_tile(grp_tile_view, 0, 4, LV_DIR_TOP);

    // page_group_tile_init(GrpTile.tile_gpr_A, &Param.group_A);
    // page_group_tile_init(GrpTile.tile_gpr_B, &Param.group_B);
    // page_group_tile_init(GrpTile.tile_gpr_C, &Param.group_C);
    // page_group_tile_init(GrpTile.tile_gpr_D, &Param.group_D);
    // page_group_tile_init(GrpTile.tile_gpr_E, &Param.group_E);

#if 0
    GroupStruct *first_added = NULL, *last_added = NULL;

    find_first_and_last_added_groups(&Param, &first_added, &last_added);

    if (first_added) {
        LV_LOG("First added group: %d\n", first_added->group_name);
        switch (first_added->group_name) {
            case group_A:
                GrpTile.tile_gpr_A->dir = LV_DIR_BOTTOM;
                break;
            case group_B:
                GrpTile.tile_gpr_B->dir = LV_DIR_BOTTOM;
                break;
            case group_C:
                GrpTile.tile_gpr_C->dir = LV_DIR_BOTTOM;
                break;
            case group_D:
                GrpTile.tile_gpr_D->dir = LV_DIR_BOTTOM;
                break;
            case group_E:
                GrpTile.tile_gpr_E->dir = LV_DIR_BOTTOM;
                break;
        }
    } else {
        LV_LOG("No groups added.\n");
    }

    if (last_added && last_added != first_added) {
        LV_LOG("Last added group: %d\n", last_added->group_name);
        switch (first_added->group_name) {
            case group_A:
                GrpTile.tile_gpr_A->dir = LV_DIR_TOP;
                break;
            case group_B:
                GrpTile.tile_gpr_B->dir = LV_DIR_TOP;
                break;
            case group_C:
                GrpTile.tile_gpr_C->dir = LV_DIR_TOP;
                break;
            case group_D:
                GrpTile.tile_gpr_D->dir = LV_DIR_TOP;
                break;
            case group_E:
                GrpTile.tile_gpr_E->dir = LV_DIR_TOP;
                break;
        }
    }
#endif
    // if (Param.group_A.is_added_to_list) {
    //     lv_obj_add_flag((lv_obj_t *) GrpTile.tile_gpr_A, LV_OBJ_FLAG_HIDDEN);
    // } else {
    //     lv_obj_clear_flag((lv_obj_t *) GrpTile.tile_gpr_A, LV_OBJ_FLAG_HIDDEN);
    // }
    // if (Param.group_B.is_added_to_list) {
    //     lv_obj_add_flag((lv_obj_t *) GrpTile.tile_gpr_B, LV_OBJ_FLAG_HIDDEN);
    // } else {
    //     lv_obj_clear_flag((lv_obj_t *) GrpTile.tile_gpr_B, LV_OBJ_FLAG_HIDDEN);
    // }
    // if (Param.group_C.is_added_to_list) {
    //     lv_obj_add_flag((lv_obj_t *) GrpTile.tile_gpr_C, LV_OBJ_FLAG_HIDDEN);
    // } else {
    //     lv_obj_clear_flag((lv_obj_t *) GrpTile.tile_gpr_C, LV_OBJ_FLAG_HIDDEN);
    // }
    // if (Param.group_D.is_added_to_list) {
    //     lv_obj_add_flag((lv_obj_t *) GrpTile.tile_gpr_D, LV_OBJ_FLAG_HIDDEN);
    // } else {
    //     lv_obj_clear_flag((lv_obj_t *) GrpTile.tile_gpr_D, LV_OBJ_FLAG_HIDDEN);
    // }
    // if (Param.group_E.is_added_to_list) {
    //     lv_obj_add_flag((lv_obj_t *) GrpTile.tile_gpr_E, LV_OBJ_FLAG_HIDDEN);
    // }
    // update_all_group_tiles();

    // lv_obj_clear_flag((lv_obj_t *) grp_tile_view, LV_OBJ_FLAG_SCROLLABLE);

    // lv_obj_add_flag((lv_obj_t *) tile_gpr_B, LV_OBJ_FLAG_HIDDEN);
    // tile_gpr_A->dir = LV_DIR_TOP;
    // lv_obj_del(tile_gpr_E);
    // tile_gpr_E = lv_tileview_add_tile(grp_tile_view, 0, 4, LV_DIR_TOP);
    // lv_obj_t *aaa = lv_obj_create(page);

    // lv_obj_del(tile_gpr_B);

    // 手动滚动到指定的 tile
    // lv_obj_scroll_to(grp_tile_view, 0, (Screen_Height - status_bar_h) * 2, LV_ANIM_ON);
    // lv_obj_set_tile(grp_tile_view, (lv_obj_t *) tile_gpr_D, LV_ANIM_OFF);

    // 添加下拉手柄
    handle_group_info_left = pm_create_handle(page, pos_left);
    handle_group_info_top = pm_create_handle(page, pos_top);

    // 左手柄视觉对象
    left_bar_init(page);
}
