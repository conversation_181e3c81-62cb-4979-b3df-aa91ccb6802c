//
// Created by <PERSON><PERSON> on 2025/3/23.
//

#include "multi_language.h"
#include "user.h"

// 中文文本
static const char *chinese_texts[Text_Count] = {
        // Regular 22 & opt
        [text_distance_warning] = "* 距离选择有误可能导致大量漏闪",
        [text_beep_info] = "* 接收端的回电提示音",
        [text_wireless_sync_info] = "请查看接收端是否同步",
        [text_factory_reset_warning] = "所有数据将被还原至初始化",
        [text_factory_resetting] = "正在初始化设备...",
        [text_unlock_info] = "长按2秒解锁",
        // Regular 30
        [text_model] = "型    号: ",
        [text_version] = "版本号: ",
        [text_channel] = "频道",
        [text_id] = "识别码",
        [text_min_power] = "最小功率",
        [text_step] = "步进",
        [text_standby] = "待机",
        [text_shutdown] = "关机",
        [text_factory_reset] = "恢复出厂设置",
        [text_scan_channel] = "扫描频道",
        [text_wireless_sync] = "无线同步",
        [text_confirm] = "确认",
        [text_cancel] = "取消",
        [text_brightness] = "亮度",
        // Bold 32 & opt
        [text_front_curtain_sync] = "前帘同步",
        [text_rear_curtain_sync] = "后帘同步",
        [text_high_speed_sync] = "高速同步",
        [text_auto] = "自动",
        [text_language] = "语言",
        // Regular 40
        [text_menu] = "菜单",
        // Bold 40 & text_auto
        [text_more] = "更多组别",
        [text_reset] = "重置",
        [text_off] = "关闭",
        // opt
        [text_standby_option] = "15秒\n30秒\n1分钟\n2分钟\n3分钟",
        [text_shutdown_option] = "关闭\n30分钟\n60分钟\n90分钟",
        [text_id_option] = "关闭\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n"
                           "21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n"
                           "41\n42\n43\n44\n45\n46\n47\n48\n49\n50\n51\n52\n53\n54\n55\n56\n57\n58\n59\n60\n"
                           "61\n62\n63\n64\n65\n66\n67\n68\n69\n70\n71\n72\n73\n74\n75\n76\n77\n78\n79\n80\n"
                           "81\n82\n83\n84\n85\n86\n87\n88\n89\n90\n91\n92\n93\n94\n95\n96\n97\n98\n99",
};

// 英文文本
static const char *english_texts[Text_Count] = {
        // [text_distance_warning] = "* Improper distance selection may result in significant missed flashes.",
        [text_distance_warning] = "* Incorrect distance selection may cause severe flash misfires.",
        // [text_beep_info] = "* Recharge alert sound of the controlled flash.",
        // https://www.canon.com.hk/cpx/en/technical/pa_600EX_RT_Beep.html
        [text_beep_info] = "* Recycle beep of the receiver.",
        [text_min_power] = "Min. Power",
        [text_step] = "Step",
        [text_scan_channel] = "Scan Channels",
        [text_channel] = "CH",
        [text_id] = "ID",
        [text_wireless_sync] = "Wireless Sync",
        [text_front_curtain_sync] = "Front Curtain",
        [text_rear_curtain_sync] = "Rear Curtain",
        [text_high_speed_sync] = "High Speed",
        [text_standby] = "Standby",
        [text_shutdown] = "Shutdown",
        [text_auto] = "AUTO",
        [text_language] = "Language",
        [text_wireless_sync_info] = "Please confirm wireless sync on the receiver",
        [text_factory_reset] = "Factory Reset",
        // [text_factory_reset_warning] = "All data will be restored to initialization",
        [text_factory_reset_warning] = "All data will be reset to default settings.",
        // [text_factory_resetting] = "Initializing the device...",
        [text_factory_resetting] = "Resetting the device...",
        [text_confirm] = "Confirm",
        [text_cancel] = "Cancel",
        [text_standby_option] = "15s\n30s\n1min\n2min\n3min",
        [text_shutdown_option] = "OFF\n30min\n60min\n90min",
        [text_unlock_info] = "Press and hold for 2s to unlock.",
        [text_menu] = "Menu",
        [text_more] = "More",
        [text_brightness] = "Brightness",
        [text_version] = "Version: ",
        [text_model] = "Model:   ",
        [text_reset] = "RES",
        [text_off] = "OFF",
        [text_id_option] = "OFF\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n"
                           "21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n"
                           "41\n42\n43\n44\n45\n46\n47\n48\n49\n50\n51\n52\n53\n54\n55\n56\n57\n58\n59\n60\n"
                           "61\n62\n63\n64\n65\n66\n67\n68\n69\n70\n71\n72\n73\n74\n75\n76\n77\n78\n79\n80\n"
                           "81\n82\n83\n84\n85\n86\n87\n88\n89\n90\n91\n92\n93\n94\n95\n96\n97\n98\n99",
};

// 德语文本
static const char *german_texts[Text_Count] = {

};

// 西班牙语文本
static const char *spanish_texts[Text_Count] = {

};

// 意大利语文本
static const char *italian_texts[Text_Count] = {

};

// 法语文本
static const char *french_texts[Text_Count] = {

};

// 俄语文本
static const char *russian_texts[Text_Count] = {

};

// 荷兰语文本
static const char *dutch_texts[Text_Count] = {

};

// 日语文本
static const char *japanese_texts[Text_Count] = {

};

// 所有语言的文本数组
static const char **all_languages[Lang_Count] = {
        [lang_ch] = chinese_texts,
        [lang_en] = english_texts,
        // [lang_de] = german_texts,
        // [lang_es] = spanish_texts,
        // [lang_it] = italian_texts,
        // [lang_fr] = french_texts,
        // [lang_ru] = russian_texts,
        // [lang_ne] = dutch_texts,
        // [lang_jp] = japanese_texts
};

// 初始化多语言系统
void multi_language_init(void) {
    // 确保语言选择在有效范围内
    if (Setting.values[setting_language] >= Lang_Count) {
        // 默认使用英语
        Setting.values[setting_language] = lang_en;
    }
}

// 获取当前语言的枚举值
LanguageEnum get_current_language(void) {
    // 确保语言选择在有效范围内
    if (Setting.values[setting_language] >= Lang_Count) {
        // 默认使用英语
        Setting.values[setting_language] = lang_en;
    }

    return (LanguageEnum) Setting.values[setting_language];
}

// 通过文本ID获取当前语言的文本
char *get_text(TextIdEnum text_id) {
    // 检查文本ID是否有效
    if (text_id >= Text_Count) {
        return "Error";
    }

    // 获取当前语言设置
    LanguageEnum current_lang = get_current_language();

    // 获取当前语言的文本
    const char *text = all_languages[current_lang][text_id];

    // 如果文本为NULL或空字符串，则返回英文文本
    // 如果英文也为空，则返回错误
    if (text == NULL || text[0] == '\0') {
        const char *english_text = all_languages[lang_en][text_id];
        if (english_text == NULL || english_text[0] == '\0') {
            return "Error";
        }
        return (char *) english_text;
    }

    return (char *) text;
}

// 切换语言
void set_language(LanguageEnum lang) {
    if (lang < Lang_Count) {
        // 只更新系统设置中的语言值
        Setting.values[setting_language] = (uint8_t) lang;
    }
}