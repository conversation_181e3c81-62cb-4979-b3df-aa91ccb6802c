//
// Created by <PERSON><PERSON> on 2024/7/2.
//

#include "comp_slider.h"
#include "comp_border_focused.h"
#include "utils.h"
#include "view_model.h"
#include "comp_status_bar.h"
#include "multi_language.h"
#include "comp_grp_switch.h"

// 滑动条配置结构体
typedef struct {
    SliderTypeEnum slider_type;
    LevelTypeEnum level_type;
    PageNameEnum page_type;
    const void *img_src;  // 仅用于option类型
    bool create_group_area;  // 是否创建组名区域
    bool use_real_slider;    // 是否使用真实滑动条（my_slider）
    lv_coord_t container_height;  // 容器高度
    uint8_t focused_flags;   // 聚焦边框标志
} SliderConfig;

//  实机滑动条存在死机问题
//  疑似label导致,存在部分时候label的值不改变,仅改变滑条宽度,此时点击其他组别必定死机,
//  TTL下反复滑动极易复现
//  确认为label的str动态内存分配的问题

// TODO 手柄在动画过程中连续点击效果异常
//  在主页点击进入详情页的过程中反复点击左手柄
//  考虑开始动画时禁用手柄点击flag,动画结束后再激活,可避免动画打断

// 滑动条容器左右边距
const uint8_t slider_layout_padding = 18;
const uint8_t slider_layout_option_padding = 6;
const lv_coord_t grp_name_area_width = 54;
const lv_coord_t slider_comp_height = 100;
const lv_coord_t slider_comp_option_height = 86;
const lv_coord_t opt_slider_layout_height = slider_comp_option_height * 3 + slider_layout_option_padding * 2;
// 局部非静态数组未初始化为随机数，全局数组为0
static uint16_t pressing_count;
static bool is_short_clicked;

/**
 * @brief
 * @param slider slider对象
 * @param slider_range_type slider范围类型
 */
void slider_set_range(lv_obj_t *slider, const SliderRangeEnum slider_range_type) {
    switch (slider_range_type) {
        case range_m:
            lv_slider_set_range(slider, 0, get_m_level_max());
            break;
        case range_ttl:
            lv_slider_set_range(slider, 0, MAX_FLASH_LEVEL_TTL);
            break;
        case range_tcm:
            lv_slider_set_range(slider, 0, MAX_FLASH_LEVEL_M_TCM);
            break;
        case range_multi:
            lv_slider_set_range(slider, 0, get_multi_level_range());
            break;
        case range_lamp:
            lv_slider_set_range(slider, 0, MAX_LAMP_LEVEL);
            break;
        case range_zoom:
            lv_slider_set_range(slider, 0, MAX_ZOOM_LEVEL);
            break;
        default:
            lv_slider_set_range(slider, 0, 100);
            break;
    }
}

// 更新tcm_obj状态
void update_tcm_obj(lv_obj_t *tcm_obj, GroupStruct *grp) {
    if (grp->mode == mode_M && Setting.values[setting_tcm] && Param.ctrl_type == type_flash &&
        grp->group_name <= group_C) {
        lv_obj_clear_flag(tcm_obj, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(tcm_obj, LV_OBJ_FLAG_HIDDEN);
    }
}

// TCM组件
lv_obj_t *comp_tcm_init(lv_obj_t *parent) {
    lv_obj_t *tcm_obj = lv_obj_custom_trans_create(parent, 46, 24);
    lv_obj_set_style_border_width(tcm_obj, 2, 0);
    lv_obj_set_style_border_color(tcm_obj, lv_color_white(), 0);
    lv_obj_set_style_radius(tcm_obj, 6, 0);
    lv_obj_align_to(tcm_obj, parent, LV_ALIGN_OUT_RIGHT_TOP, -(46 + 11 + 4), 8 + 2);
    lv_obj_add_flag(tcm_obj, LV_OBJ_FLAG_IGNORE_LAYOUT);
    lv_obj_clear_flag(tcm_obj, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_t *tcm_label = lv_label_custom_create(tcm_obj, "TCM", FONT_TCM, lv_color_white(), LV_TEXT_ALIGN_CENTER);
    lv_obj_center(tcm_label);
    return tcm_obj;
}

// 滑动条与页面滚动冲突事件处理
void slider_parent_scrollable_handle(SliderTypeEnum slider_type, bool flag, lv_obj_t *slider_bg) {
    if (slider_type == slider_home) {
        if (flag) {
            lv_obj_add_flag(slider_bg->parent->parent, LV_OBJ_FLAG_SCROLLABLE);
        } else {
            lv_obj_clear_flag(slider_bg->parent->parent, LV_OBJ_FLAG_SCROLLABLE);
        }
    } else if (slider_type == slider_option) {
        if (flag) {
            lv_obj_add_flag(Param.grp_tile_view, LV_OBJ_FLAG_SCROLLABLE);
        } else {
            lv_obj_clear_flag(Param.grp_tile_view, LV_OBJ_FLAG_SCROLLABLE);
        }
    }
}

// 切换span_group和label
void switch_label_obj(char *sub_level_str, lv_obj_t *span_group, lv_obj_t *level_label, GroupStruct *group) {
    if (strcmp(sub_level_str, "") == 0 || (Setting.values[setting_tcm] && group->group_name <= group_C)) {
        lv_obj_add_flag(span_group, LV_OBJ_FLAG_HIDDEN);
        lv_obj_clear_flag(level_label, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(level_label, LV_OBJ_FLAG_HIDDEN);
        lv_obj_clear_flag(span_group, LV_OBJ_FLAG_HIDDEN);
    }
}

// 切换模式label和造型灯img
void switch_type_obj(lv_obj_t *mode_label, lv_obj_t *lamp_img) {
    if (Param.ctrl_type == type_flash) {
        lv_obj_clear_flag(mode_label, LV_OBJ_FLAG_HIDDEN);
        lv_obj_add_flag(lamp_img, LV_OBJ_FLAG_HIDDEN);
    } else if (Param.ctrl_type == type_lamp) {
        lv_obj_clear_flag(lamp_img, LV_OBJ_FLAG_HIDDEN);
        lv_obj_add_flag(mode_label, LV_OBJ_FLAG_HIDDEN);
    }
}

// 滑动条事件处理
static void slider_event_handler(lv_event_t *e) {
    // LV_LOG_USER("slider_event_handler");
    lv_obj_t *slider = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);

    SliderStruct *slider_struct;
    // 防止slider_struct->level_max指向为空导致的死机
    if (lv_obj_get_user_data(slider) != NULL) {
        slider_struct = lv_obj_get_user_data(slider);
    } else {
        return;
    }

    uint8_t slider_level = lv_slider_get_value(slider);
    uint8_t *flash_level = get_flash_level(slider_struct->group);
    const uint8_t event_param = (uintptr_t) lv_event_get_param(e);

    if (code == LV_EVENT_PRESSING) {
        LV_LOG_USER("slider_cb LV_EVENT_PRESSING");
        lv_point_t vect;
        lv_indev_get_vect(lv_indev_get_act(), &vect);
        // LV_LOG("vect.x: %d, vect.y: %d\n", vect.x, vect.y);
        pressing_count++;
        // LV_LOG("count_pressing: %d, ", pressing_count);
        if (LV_ABS(vect.x) < 2 && LV_ABS(vect.y) < 2 && pressing_count < Short_Click_Sensitivity) {
            // LV_LOG("is_short_clicked\n");
            is_short_clicked = true;
        } else {
            // LV_LOG("not_short_clicked\n");
            is_short_clicked = false;
        }
    } else if (code == LV_EVENT_RELEASED) {
        pressing_count = 0;
    } else if (code == LV_EVENT_VALUE_CHANGED) {
        LV_LOG_USER("slider_LV_EVENT_VALUE_CHANGED");
        LV_LOG_USER("slider_struct->level_type: %d", slider_struct->level_type);
        // 预留足够的空间存储结果，最大为 "+0.9\0"
        // 预留足够的空间存储结果，最大为 "200mm\0"
        char level_str[20], sub_level_str[6];
        // 造型灯滑条处理
        if (slider_struct->level_type == level_lamp) {
            LV_LOG_USER("slider_struct->group->stylish_lamp_level: %d", slider_struct->group->stylish_lamp_level);
            if (event_param != 1) {
                slider_struct->group->stylish_lamp_level = slider_level;
            }
            lv_color_t label_color = lv_color_white();
            if (slider_struct->group->stylish_lamp_mode == lamp_off) {
                strncpy(level_str, get_text(text_off), sizeof(level_str));
                label_color = lv_color_hex(OFF_Color);
                if (Setting.lamp_main_switch) {
                    bool lamp_main_switch_check = false;
                    for (int i = 0; i < GroupItemCount; ++i) {
                        if (Param.group[i].is_added_to_list && Param.group[i].stylish_lamp_mode != lamp_off) {
                            lamp_main_switch_check = true;
                        }
                    }
                    if (!lamp_main_switch_check) {
                        Setting.lamp_main_switch = false;
                        update_status_bar();
                    }
                }
            } else {
                if (!Setting.lamp_main_switch) {
                    Setting.lamp_main_switch = true;
                    update_status_bar();
                }
#if LAMP_PROP
                if (slider_struct->group->stylish_lamp_mode == lamp_prop) {
                    slider_level_width = slider_width;
                    strncpy(level_str, "PROP", sizeof(level_str));
                } else
#endif
                if (slider_struct->group->stylish_lamp_mode == lamp_manual) {
                    // 强制设值
                    if (slider_level < 1) {
                        lv_slider_set_value(slider, 1, LV_ANIM_OFF);
                        slider_struct->group->stylish_lamp_level = 1;
                    }
                    lv_slider_set_value(slider, slider_struct->group->stylish_lamp_level, LV_ANIM_OFF);
                    sprintf(level_str, "%d0%%", slider_struct->group->stylish_lamp_level);
                }
            }

            if (slider_struct->slider_type == slider_home) {
                slider_set_range(slider, range_lamp);
                switch_label_obj("", slider_struct->group->slider_home.span_group,
                                 slider_struct->group->slider_home.level_label, slider_struct->group);
                lv_label_set_text(slider_struct->group->slider_home.level_label, level_str);
                lv_obj_set_style_text_color(slider_struct->group->slider_home.level_label, label_color, 0);
                if (slider_struct->group->stylish_lamp_mode == lamp_off) {
                    lv_obj_clear_flag(slider_struct->group->slider_home.mask, LV_OBJ_FLAG_HIDDEN);
                    // 关闭造型灯时，强制设滑条为0
                    lv_slider_set_value(slider, 0, LV_ANIM_OFF);
                } else {
                    lv_obj_add_flag(slider_struct->group->slider_home.mask, LV_OBJ_FLAG_HIDDEN);
                }
                // 更新tcm obj，避免ctrl_type切换未更新tcm_obj状态
                update_tcm_obj(slider_struct->group->slider_home.tcm_obj, slider_struct->group);
            } else if (slider_struct->slider_type == slider_option) {
                lv_label_set_text(slider_struct->group->slider_option_lamp_level.level_label, level_str);
                lv_obj_set_style_text_color(slider_struct->group->slider_option_lamp_level.level_label, label_color, 0);
            }
            set_beep_lamp_level();
        } else if (slider_struct->level_type == level_zoom) {
            // 焦距滑条处理
            if (slider_struct->group->is_auto_zoom) {
                lv_slider_set_value(slider, MAX_ZOOM_LEVEL, LV_ANIM_OFF);
                strncpy(level_str, get_text(text_auto), sizeof(level_str));
            } else {
                lv_slider_set_value(slider, slider_struct->group->zoom_level, LV_ANIM_OFF);
                strncpy(level_str, zoom_level_text[slider_struct->group->zoom_level], sizeof(level_str));
#if ProductModel == QZ_F
                if (Setting.values[setting_zoom_disp] == zoom_aps) {
                    strncpy(level_str, zoom_aps_level_text[slider_struct->group->zoom_level], sizeof(level_str));
                }
#endif
            }
            lv_label_set_text(slider_struct->group->slider_option_zoom_level.level_label, level_str);
            set_zoom_level(slider_struct->group->group_name);
        } else {
            // 闪光能级滑条处理
            if (slider_struct->level_type == level_flash && event_param != 1) {
                *flash_level = slider_level;
            }
            if (slider_struct->group->mode == mode_M) {
                if (Setting.values[setting_tcm] && slider_struct->group->group_name <= group_C) {
                    slider_set_range(slider, range_tcm);
                    if (slider_struct->group->flash_level_M_tcm > 0) {
                        snprintf(level_str, sizeof(level_str), "+%d.%d", slider_struct->group->flash_level_M_tcm / 10,
                                 slider_struct->group->flash_level_M_tcm % 10);
                    } else if (slider_struct->group->flash_level_M_tcm < 0) {
                        snprintf(level_str, sizeof(level_str), "-%d.%d", -slider_struct->group->flash_level_M_tcm / 10,
                                 -slider_struct->group->flash_level_M_tcm % 10);
                    } else {
                        snprintf(level_str, sizeof(level_str), "0.0");
                    }
                } else {
                    slider_set_range(slider, range_m);
                    // Setting.roller_values[roller_min_power]前三项为分数
                    if (Setting.roller_values[roller_min_power] / MAX_FLASH_LEVEL_INDEX == 0) {
                        strncpy(level_str, get_str_flash_level_M(*flash_level), sizeof(level_str));
                        get_str_flash_sub_level_M(*flash_level, sub_level_str, sizeof(sub_level_str));
                    } else {
                        strncpy(level_str, get_decimals_str_flash_level_M(*flash_level), sizeof(level_str));
                        sub_level_str[0] = '\0';
                    }
                }
            } else if (slider_struct->group->mode == mode_TTL) {
                slider_set_range(slider, range_ttl);
                strncpy(level_str, get_str_flash_level_TTL(*flash_level), sizeof(level_str));
                sub_level_str[0] = '\0';
            } else {
                // level_str = "----";
                strncpy(level_str, "----", sizeof(level_str));
                sub_level_str[0] = '\0';
            }
            // 确保以 '\0' 结尾
            level_str[sizeof(level_str) - 1] = '\0';

            LV_LOG("--flash_level %d\n", *flash_level);
            // 设置滑动条宽度
            // lv_obj_set_width(slider, slider_level_width);
            lv_slider_set_value(slider, *flash_level, LV_ANIM_OFF);

            if (slider_struct->slider_type == slider_home) {
                mode_label_color_handle(slider_struct->group->slider_home.level_label,
                                        slider_struct->group->mode);
                lv_label_set_text(slider_struct->group->slider_home.level_label, level_str);
                lv_span_set_text(slider_struct->group->slider_home.span_group_level, level_str);
                lv_span_set_text(slider_struct->group->slider_home.span_group_sub_level, sub_level_str);
                switch_label_obj(sub_level_str, slider_struct->group->slider_home.span_group,
                                 slider_struct->group->slider_home.level_label, slider_struct->group);
                // 更新tcm obj
                update_tcm_obj(slider_struct->group->slider_home.tcm_obj, slider_struct->group);

                mode_label_color_handle(slider_struct->group->slider_home.mode_label, slider_struct->group->mode);
                lv_label_set_text(slider_struct->group->slider_home.mode_label,
                                  mode_text[slider_struct->group->mode]);
                if (slider_struct->group->mode == mode_OFF) {
                    lv_obj_clear_flag(slider_struct->group->slider_home.mask, LV_OBJ_FLAG_HIDDEN);
                } else {
                    lv_obj_add_flag(slider_struct->group->slider_home.mask, LV_OBJ_FLAG_HIDDEN);
                }
            } else if (slider_struct->slider_type == slider_option) {
                mode_label_color_handle(slider_struct->group->slider_option_flash_level.level_label,
                                        slider_struct->group->mode);
                lv_label_set_text(slider_struct->group->slider_option_flash_level.level_label, level_str);
                lv_span_set_text(slider_struct->group->slider_option_flash_level.span_group_level, level_str);
                lv_span_set_text(slider_struct->group->slider_option_flash_level.span_group_sub_level, sub_level_str);
                switch_label_obj(sub_level_str, slider_struct->group->slider_option_flash_level.span_group,
                                 slider_struct->group->slider_option_flash_level.level_label, slider_struct->group);
                // 更新tcm obj
                update_tcm_obj(slider_struct->group->slider_option_flash_level.tcm_obj, slider_struct->group);
            }
            set_flash_mode_level();
        }
    } else if (code == LV_EVENT_SHORT_CLICKED) {
        if (!is_short_clicked) {
            return;
        }
        uint8_t param = (uintptr_t) lv_event_get_param(e);
        if (param == 0xFF) {
            LV_LOG("param == true\n");
        }
        LV_LOG("Anim.is_finished_anim: %d\n", Anim.is_finished_anim);
        if (Anim.is_finished_anim) {
            LV_LOG("SHORT_CLICKED\n");
            if (slider_struct->slider_type == slider_home) {
                // 创建页面
                pm_creat_page(PageManager.main_page->pos[pos_bottom].page, PageManager.main_page,
                              LV_ALIGN_OUT_RIGHT_MID,
                              lv_color_black());
                if (slider_struct->group->tile_gpr != NULL) {
                    // 不更新会导致set_tile无效，始终跳id = 0的tile
                    lv_obj_update_layout(Param.grp_tile_view);
                    lv_obj_set_tile(Param.grp_tile_view, (lv_obj_t *) slider_struct->group->tile_gpr, LV_ANIM_OFF);
                    LV_LOG("NOT NULL\n");
                    LV_LOG("grp_name %s\n", group_text[slider_struct->group->group_name]);
                    if (slider_struct->group->tile_gpr == Param.group[group_A].tile_gpr) {
                        LV_LOG("grp_name %s\n", group_text[slider_struct->group->group_name]);
                    }
                }
                // 详情页隐藏状态栏
                // lv_obj_set_parent(status_bar, PageManager.main_page->obj);
                page_click_anim(pos_right, &Pages.page[Page_Group_Info], anim_slide);
            } else if (slider_struct->slider_type == slider_option) {
                // 切换自动模式
                if (slider_struct->level_type == level_zoom) {
                    slider_struct->group->is_auto_zoom = !slider_struct->group->is_auto_zoom;
                } else if (slider_struct->level_type == level_lamp) {
                    lamp_mode_switch_handle(slider_struct->group);
                }
                lv_event_send(slider, LV_EVENT_VALUE_CHANGED, NULL);
            }
        }
    } else if (code == LV_EVENT_KEY) {
        char c = *((char *) lv_event_get_param(e));
        // LV_LOG("c %d\n", c);
        if (c == LV_KEY_LEFT) {
            if (slider_struct->level_type == level_lamp) {
                slider_struct->group->stylish_lamp_level = level_adjust_handle(1,
                                                                               slider_struct->group->stylish_lamp_level,
                                                                               MIN_LAMP_LEVEL, MAX_LAMP_LEVEL, 1);
            } else if (slider_struct->level_type == level_zoom) {
                if (!slider_struct->group->is_auto_zoom) {
                    slider_struct->group->zoom_level =
                            level_adjust_handle(1, slider_struct->group->zoom_level, 0, MAX_ZOOM_LEVEL, 1);
                }
            } else {
                if (Setting.values[setting_tcm] && slider_struct->group->group_name <= group_C && slider_struct->group->
                    mode == mode_M) {
                    int8_t new_tcm = slider_struct->group->flash_level_M_tcm;
                    new_tcm = (int8_t) level_adjust_handle(1, new_tcm, MIN_FLASH_LEVEL_M_TCM_INT_8,
                                                           MAX_FLASH_LEVEL_M_TCM_INT_8, 1);
                    slider_struct->group->flash_level_M_tcm = new_tcm;
                } else {
                    *flash_level = level_adjust_handle(1, *flash_level, 0,
                                                       get_flash_level_max(slider_struct->group), 1);
                    if (slider_struct->group->mode == mode_M) {
                        // 应用0.3步进处理
                        *flash_level = level_step_0_3_handle(1, *flash_level);
                    }
                }
            }
        } else if (c == LV_KEY_RIGHT) {
            if (slider_struct->level_type == level_lamp) {
                slider_struct->group->stylish_lamp_level = level_adjust_handle(-1,
                                                                               slider_struct->group->stylish_lamp_level,
                                                                               MIN_LAMP_LEVEL, MAX_LAMP_LEVEL, 1);
            } else if (slider_struct->level_type == level_zoom) {
                if (!slider_struct->group->is_auto_zoom) {
                    slider_struct->group->zoom_level =
                            level_adjust_handle(-1, slider_struct->group->zoom_level, 0, MAX_ZOOM_LEVEL, 1);
                }
            } else {
                if (Setting.values[setting_tcm] && slider_struct->group->group_name <= group_C && slider_struct->group->
                    mode == mode_M) {
                    int8_t new_tcm = slider_struct->group->flash_level_M_tcm;
                    new_tcm = (int8_t) level_adjust_handle(-1, new_tcm, MIN_FLASH_LEVEL_M_TCM_INT_8,
                                                           MAX_FLASH_LEVEL_M_TCM_INT_8, 1);
                    slider_struct->group->flash_level_M_tcm = new_tcm;
                } else {
                    *flash_level = level_adjust_handle(-1, *flash_level, 0,
                                                       get_flash_level_max(slider_struct->group), 1);
                    if (slider_struct->group->mode == mode_M) {
                        // 应用0.3步进处理
                        *flash_level = level_step_0_3_handle(-1, *flash_level);
                    }
                }
            }
        } else if (c == LV_KEY_ESC) {
            if (PageManager.main_page == &Pages.page[Page_Home]) {
                if (lv_group_get_editing(lv_group_get_default())) {
                    if (Param.ctrl_type == type_flash) {
                        mode_switch_handle(slider_struct->group);
                    } else if (Param.ctrl_type == type_lamp) {
                        lamp_mode_switch_handle(slider_struct->group);
                    }
                    lv_event_send(slider, LV_EVENT_VALUE_CHANGED, NULL);
                } else {
                    // 创建页面
                    pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page,
                                  LV_ALIGN_OUT_LEFT_MID,
                                  lv_color_black());
                    page_click_anim(pos_left, &Pages.page[Page_Multi], anim_slide);
                }
            } else if (PageManager.main_page == &Pages.page[Page_Group_Info]) {
                if (lv_group_get_editing(lv_group_get_default())) {
                    // 编辑状态下切为AUTO
                    if (slider_struct->level_type == level_zoom || slider_struct->level_type == level_lamp) {
                        uint8_t param = 0xFF;
                        lv_event_send(slider, LV_EVENT_SHORT_CLICKED, (void *) (uintptr_t) param);
                    }
                }
            }
        } else if (c == LV_KEY_HOME) {
            if (PageManager.main_page == &Pages.page[Page_Home]) {
                // // 编辑状态长按进详情页
                // if (lv_group_get_editing(lv_group_get_default())) {
                uint8_t param = 0xFF;
                lv_event_send(slider, LV_EVENT_SHORT_CLICKED, (void *) (uintptr_t) param);
                // } else {
                //     // 创建页面
                //     pm_creat_page(PageManager.main_page->top.page, PageManager.main_page, LV_ALIGN_OUT_TOP_MID,
                //                   lv_color_black());
                //     page_click_anim(pos_top, &Pages.page[Page_Control_Center], anim_overlay);
                // }
            } else if (PageManager.main_page == &Pages.page[Page_Group_Info]) {
                // 创建页面
                pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page, LV_ALIGN_OUT_LEFT_MID,
                              lv_color_black());
                page_click_anim(pos_left, &Pages.page[Page_Home], anim_slide);
            }
        } else if (c == LV_KEY_END) {
            // 创建页面
            pm_click_to_page_control_center();
        }
        if (c != LV_KEY_HOME) {
            lv_event_send(slider, LV_EVENT_VALUE_CHANGED, NULL);
        }
    }
}

// 模式文字obj和造型灯图片obj切换回调
static void type_obj_event_cb(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    TypeObjStruct *type_obj_struct;
    // 防止user_data指向为空导致的死机
    if (lv_obj_get_user_data(tar_obj) != NULL) {
        type_obj_struct = lv_obj_get_user_data(tar_obj);
    } else {
        return;
    }
    switch_type_obj(type_obj_struct->mode_label, type_obj_struct->lamp_img);
}

// 通用滑动条初始化函数
static SliderObjStruct comp_slider_init_common(lv_obj_t *parent, GroupStruct *group, const SliderConfig *config);

SliderObjStruct comp_group_slider_init(lv_obj_t *parent_layout, GroupStruct *group) {
    // 配置home类型滑动条
    SliderConfig config = {
        .slider_type = slider_home,
        .level_type = (Param.ctrl_type == type_lamp) ? level_lamp : level_flash,
        .page_type = Page_Home,
        .img_src = NULL,
        .create_group_area = true,
        .use_real_slider = true,
        .container_height = slider_comp_height,
        .focused_flags = Focused_Scroll_Parent | Focused_Editing_Mode
    };

    return comp_slider_init_common(parent_layout, group, &config);
}

LevelTypeEnum get_level_type(const void *img_src) {
    LevelTypeEnum level_type;
    if (img_src == &icon_lamp) {
        level_type = level_lamp;
    } else if (img_src == &icon_zoom) {
        level_type = level_zoom;
    } else {
        level_type = level_flash;
    }
    return level_type;
}

SliderObjStruct comp_option_slider_init(lv_obj_t *parent, const void *img_src, GroupStruct *group) {
    // 配置option类型滑动条
    SliderConfig config = {
        .slider_type = slider_option,
        .level_type = get_level_type(img_src),
        .page_type = Page_Group_Info,
        .img_src = img_src,
        .create_group_area = false,
        .use_real_slider = true,  // 改为使用真实滑动条
        .container_height = slider_comp_option_height,
        .focused_flags = Focused_Scroll_Tile | Focused_Editing_Mode
    };

    return comp_slider_init_common(parent, group, &config);
}

// 通用滑动条初始化函数实现
static SliderObjStruct comp_slider_init_common(lv_obj_t *parent, GroupStruct *group, const SliderConfig *config) {
    SliderObjStruct SliderOption = {0};
    uint8_t level_max;
    uint8_t *level = NULL;
    char level_str[20], sub_level_str[6];

    // 根据level_type生成level_str和sub_level_str
    if (config->level_type == level_lamp) {
        if (group->stylish_lamp_mode == lamp_off) {
            strncpy(level_str, get_text(text_off), sizeof(level_str));
        }
#if LAMP_PROP
        else if (group->stylish_lamp_mode == lamp_prop) {
            strncpy(level_str, "PROP", sizeof(level_str));
        }
#endif
        else if (group->stylish_lamp_mode == lamp_manual) {
            level = &group->stylish_lamp_level;
            sprintf(level_str, "%d0%%", *level);
        }
        level_max = MAX_LAMP_LEVEL;
        sub_level_str[0] = '\0';
    } else if (config->level_type == level_zoom) {
        if (group->is_auto_zoom) {
            strncpy(level_str, get_text(text_auto), sizeof(level_str));
        } else {
            level = &group->zoom_level;
            strncpy(level_str, zoom_level_text[*level], sizeof(level_str));
#if ProductModel == QZ_F
            if (Setting.values[setting_zoom_disp] == zoom_aps) {
                strncpy(level_str, zoom_aps_level_text[*level], sizeof(level_str));
            }
#endif
        }
        level_max = MAX_ZOOM_LEVEL;
        sub_level_str[0] = '\0';
    } else { // level_flash
        level = get_flash_level(group);
        level_max = get_flash_level_max(group);
        if (group->mode == mode_M) {
            if (Setting.values[setting_tcm] && group->group_name <= group_C) {
                if (group->flash_level_M_tcm > 0) {
                    snprintf(level_str, sizeof(level_str), "+%d.%d", group->flash_level_M_tcm / 10,
                             group->flash_level_M_tcm % 10);
                } else if (group->flash_level_M_tcm < 0) {
                    snprintf(level_str, sizeof(level_str), "-%d.%d", -group->flash_level_M_tcm / 10,
                             -group->flash_level_M_tcm % 10);
                } else {
                    snprintf(level_str, sizeof(level_str), "0.0");
                }
                sub_level_str[0] = '\0';
            } else {
                if (Setting.roller_values[roller_min_power] / MAX_FLASH_LEVEL_INDEX == 0) {
                    strncpy(level_str, get_str_flash_level_M(group->flash_level_M), sizeof(level_str));
                    get_str_flash_sub_level_M(group->flash_level_M, sub_level_str, sizeof(sub_level_str));
                } else {
                    strncpy(level_str, get_decimals_str_flash_level_M(group->flash_level_M), sizeof(level_str));
                    sub_level_str[0] = '\0';
                }
            }
        } else if (group->mode == mode_TTL) {
            strncpy(level_str, get_str_flash_level_TTL(group->flash_level_TTL), sizeof(level_str));
            sub_level_str[0] = '\0';
        } else {
            if (config->slider_type == slider_home) {
                strncpy(level_str, "----", sizeof(level_str));
            } else {
                strncpy(level_str, "-----", sizeof(level_str));
            }
            sub_level_str[0] = '\0';
        }
    }

    // 确保以 '\0' 结尾
    level_str[sizeof(level_str) - 1] = '\0';

    // 检查是否需要创建滑动条（对于group类型，如果未添加到列表则不创建）
    if (config->slider_type == slider_home && !group->is_added_to_list) {
        return SliderOption;
    }

    // 创建主容器
    lv_obj_t *main_container = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW);
    lv_obj_set_size(main_container, LV_PCT(100), config->container_height);
    lv_obj_set_style_bg_opa(main_container, LV_OPA_COVER, 0);
    lv_obj_set_style_bg_color(main_container, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_set_style_radius(main_container, RADIUS_DEFAULT, 0);
    lv_obj_set_style_border_width(main_container, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(main_container, true, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_gap(main_container, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    // 为option类型设置用户数据
    if (config->slider_type == slider_option) {
        lv_obj_set_user_data(main_container, group);
    }

    lv_obj_t *left_area = NULL;
    lv_coord_t slider_area_width;

    if (config->create_group_area) {
        // 创建组别显示区域（用于home类型）
        left_area = lv_layout_custom_create(main_container, LV_FLEX_FLOW_COLUMN);
        lv_obj_set_size(left_area, grp_name_area_width, LV_PCT(100));
        lv_obj_set_style_bg_color(left_area, lv_color_hex(Bg_Color_Gray), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_border_width(left_area, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_align(left_area, LV_ALIGN_LEFT_MID, 0, 0);
        lv_obj_set_style_radius(left_area, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_flex_align(left_area, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
        lv_obj_set_style_pad_gap(left_area, POINT_GAP, 0);
        lv_obj_clear_flag(left_area, LV_OBJ_FLAG_SCROLLABLE);

        // 组名标签
        lv_obj_t *grp_name_label = lv_label_create(left_area);
        lv_label_set_text(grp_name_label, group_text[group->group_name]);
        lv_obj_set_style_text_font(grp_name_label, FONT_HOME_GRP, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_color(grp_name_label, lv_color_white(), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_center(grp_name_label);

        // 颜色点
        lv_obj_t *grp_color_point = comp_color_point_init(left_area, group->group_name);
        if (Param.is_grp_color_on) {
            lv_obj_clear_flag(grp_color_point, LV_OBJ_FLAG_HIDDEN);
        } else {
            lv_obj_add_flag(grp_color_point, LV_OBJ_FLAG_HIDDEN);
        }

        slider_area_width = Screen_Width - slider_layout_padding * 2 - grp_name_area_width;
    } else {
        // 创建图标区域（用于option类型）
        const uint8_t icon_area_width = config->container_height - 8;
        left_area = lv_layout_custom_create(main_container, LV_FLEX_FLOW_ROW);
        lv_obj_set_size(left_area, icon_area_width, config->container_height);
        lv_obj_set_flex_align(left_area, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
        lv_obj_set_style_bg_opa(left_area, LV_OPA_COVER, 0);
        lv_obj_set_style_bg_color(left_area, lv_color_hex(Bg_Color_Gray), 0);
        lv_obj_t *img = lv_img_custom_create(left_area, config->img_src);

        slider_area_width = Screen_Width - slider_layout_padding * 2 - icon_area_width;
    }

    // 创建滑动条区域 - 现在两种类型都使用真实滑动条
    lv_obj_t *slider = my_slider_create(main_container);
    lv_obj_set_style_bg_opa(slider, LV_OPA_TRANSP, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(slider, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_size(slider, slider_area_width, LV_PCT(100));
    lv_obj_clear_flag(slider, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(slider, LV_SCROLLBAR_MODE_OFF);
    lv_obj_set_style_radius(slider, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(slider, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(slider, 0, LV_PART_KNOB);
    lv_obj_set_style_bg_color(slider, lv_color_hex(Main_Color), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(slider, LV_OPA_COVER, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_align(slider, LV_ALIGN_LEFT_MID, 0, 0);
    lv_obj_set_style_border_width(slider, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_group_remove_obj(slider);

    lv_obj_t *slider_bg = slider;

    // 添加事件回调
    lv_obj_add_event_cb(slider_bg, slider_event_handler, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(slider_bg, free_struct_cb, LV_EVENT_DELETE, NULL);

    // 创建滑动条结构体
    SliderStruct *slider_struct = (SliderStruct *) malloc(sizeof(SliderStruct));
    if (slider_struct == NULL) {
        LV_LOG("slider_struct Memory allocation failed\n");
        return SliderOption;
    }
    slider_struct->level_max = level_max;
    slider_struct->slider_type = config->slider_type;
    slider_struct->level_type = config->level_type;
    slider_struct->group = group;
    lv_obj_set_user_data(slider_bg, slider_struct);

    // 设置滑动条值和范围
    if (config->level_type == level_flash) {
        if (group->mode == mode_TTL) {
            lv_slider_set_value(slider, group->flash_level_TTL, LV_ANIM_OFF);
            slider_set_range(slider, range_ttl);
        } else if (group->mode == mode_M) {
            if (Setting.values[setting_tcm] && group->group_name <= group_C) {
                lv_slider_set_value(slider, int8_2_uint8(group->flash_level_M_tcm,
                                                         MAX_FLASH_LEVEL_M_TCM), LV_ANIM_OFF);
                slider_set_range(slider, range_tcm);
            } else {
                lv_slider_set_value(slider, group->flash_level_M, LV_ANIM_OFF);
                slider_set_range(slider, range_m);
            }
        }
    } else if (config->level_type == level_lamp) {
        slider_set_range(slider, range_lamp);
        if (group->stylish_lamp_mode == lamp_off) {
            lv_slider_set_value(slider, 0, LV_ANIM_OFF);
        } else {
            lv_slider_set_value(slider, group->stylish_lamp_level, LV_ANIM_OFF);
        }
    } else if (config->level_type == level_zoom) {
        slider_set_range(slider, range_zoom);
        if (group->is_auto_zoom) {
            lv_slider_set_value(slider, MAX_ZOOM_LEVEL, LV_ANIM_OFF);
        } else {
            lv_slider_set_value(slider, group->zoom_level, LV_ANIM_OFF);
        }
    }

    // 创建标签布局
    lv_obj_t *label_layout;
    if (config->create_group_area) {
        // home类型：在slider上创建标签布局
        label_layout = lv_obj_create(slider);
        lv_obj_add_style(label_layout, &PageManager.default_style, 0);
        lv_obj_set_style_bg_opa(label_layout, LV_OPA_TRANSP, 0);
        lv_obj_set_size(label_layout, LV_PCT(100), LV_PCT(100));
        lv_obj_set_flex_flow(label_layout, LV_FLEX_FLOW_ROW);
        lv_obj_align(label_layout, LV_ALIGN_LEFT_MID, 0, 0);
        lv_obj_set_style_pad_gap(label_layout, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_move_foreground(label_layout);
        lv_obj_set_flex_align(label_layout, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
        lv_obj_clear_flag(label_layout, LV_OBJ_FLAG_CLICKABLE);
    } else {
        // option类型：在slider上创建标签布局
        label_layout = lv_obj_create(slider);
        lv_obj_add_style(label_layout, &PageManager.default_style, 0);
        lv_obj_set_style_bg_opa(label_layout, LV_OPA_TRANSP, 0);
        lv_obj_set_size(label_layout, LV_PCT(100), LV_PCT(100));
        lv_obj_set_flex_flow(label_layout, LV_FLEX_FLOW_ROW);
        lv_obj_align(label_layout, LV_ALIGN_LEFT_MID, 0, 0);
        lv_obj_set_style_pad_gap(label_layout, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_pad_left(label_layout, 70, 0);
        lv_obj_move_foreground(label_layout);
        lv_obj_set_flex_align(label_layout, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
        lv_obj_clear_flag(label_layout, LV_OBJ_FLAG_CLICKABLE);
    }

    // 计算文本宽度
    lv_coord_t font_height = lv_font_get_line_height(FONT_SLIDER_LEVEL);
    lv_coord_t level_text_width = lv_txt_get_width("1/256", strlen("1/256"), FONT_SLIDER_LEVEL, 0,
                                                   LV_TEXT_FLAG_EXPAND);
    lv_coord_t zoom_text_width = lv_txt_get_width("135mm", strlen("135mm"), FONT_SLIDER_LEVEL, 0,
                                                  LV_TEXT_FLAG_EXPAND);
    lv_coord_t sub_level_text_width = lv_txt_get_width("+0.9", strlen("+0.9"), FONT_SLIDER_SUB_LEVEL, 0,
                                                       LV_TEXT_FLAG_EXPAND);

    // 为home类型创建type_obj（模式/造型灯切换）
    if (config->create_group_area) {
        lv_obj_t *type_obj = lv_obj_create(label_layout);
        lv_obj_add_style(type_obj, &PageManager.default_style, 0);
        lv_obj_set_style_pad_right(type_obj, 10, 0);
        lv_obj_set_size(type_obj, config->container_height + 10, LV_PCT(100));
        lv_obj_set_style_bg_opa(type_obj, LV_OPA_TRANSP, 0);
        lv_obj_clear_flag(type_obj, LV_OBJ_FLAG_CLICKABLE);
        lv_obj_add_event_cb(type_obj, type_obj_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
        lv_obj_add_event_cb(type_obj, free_struct_cb, LV_EVENT_DELETE, NULL);

        TypeObjStruct *to_struct = malloc(sizeof(TypeObjStruct));
        if (to_struct == NULL) {
            LV_LOG("to_struct Memory allocation failed\n");
            return SliderOption;
        }

        lv_obj_t *mode_label = lv_label_custom_create(type_obj, mode_text[group->mode], FONT_SLIDER_LEVEL,
                                                      lv_color_white(), LV_TEXT_ALIGN_CENTER);
        lv_obj_set_size(mode_label, LV_PCT(100), font_height);
        lv_obj_center(mode_label);

        // 造型灯图标
        lv_obj_t *lamp_img = lv_img_custom_create(type_obj, &icon_lamp);
        lv_obj_center(lamp_img);

        // 切换模式label和造型灯img
        switch_type_obj(mode_label, lamp_img);

        // 设置模式标签颜色
        if (Param.ctrl_type == type_flash) {
            if (group->mode == mode_TTL) {
                lv_obj_set_style_text_color(mode_label, lv_color_hex(TTL_Color), 0);
            } else if (group->mode == mode_M) {
                lv_obj_set_style_text_color(mode_label, lv_color_white(), 0);
            } else if (group->mode == mode_OFF) {
                lv_obj_set_style_text_color(mode_label, lv_color_hex(OFF_Color), 0);
            }
        }

        to_struct->mode_label = mode_label;
        to_struct->lamp_img = lamp_img;
        lv_obj_set_user_data(type_obj, to_struct);
        SliderOption.type_obj = type_obj;
        SliderOption.mode_label = mode_label;
    }

    // 创建level_label
    lv_coord_t label_width = (config->level_type == level_zoom) ? zoom_text_width : level_text_width;
    lv_obj_t *level_label = lv_label_custom_create(label_layout, level_str, FONT_SLIDER_LEVEL, lv_color_white(),
                                                   LV_TEXT_ALIGN_LEFT);
    lv_obj_set_size(level_label, label_width, font_height);

    // 设置标签颜色
    if (config->level_type == level_flash) {
        if (group->mode == mode_TTL) {
            lv_obj_set_style_text_color(level_label, lv_color_hex(TTL_Color), 0);
        } else if (group->mode == mode_OFF) {
            lv_obj_set_style_text_color(level_label, lv_color_hex(OFF_Color), 0);
        }
    } else if (config->level_type == level_lamp && group->stylish_lamp_mode == lamp_off) {
        lv_obj_set_style_text_color(level_label, lv_color_hex(OFF_Color), 0);
    }

    SliderOption.level_label = level_label;

    // 为flash类型创建span组件
    if (config->level_type == level_flash) {
        lv_obj_t *span_group = lv_spangroup_create(label_layout);
        lv_obj_set_size(span_group, level_text_width + sub_level_text_width, font_height);
        lv_spangroup_set_align(span_group, config->create_group_area ? LV_TEXT_ALIGN_CENTER : LV_TEXT_ALIGN_LEFT);
        lv_obj_clear_flag(span_group, LV_OBJ_FLAG_CLICKABLE);

        lv_span_t *span_group_level = lv_spangroup_new_span(span_group);
        lv_span_set_text(span_group_level, level_str);
        lv_style_set_text_color(&span_group_level->style, lv_color_white());
        lv_style_set_text_font(&span_group_level->style, FONT_SLIDER_LEVEL);

        lv_span_t *span_group_sub_level = lv_spangroup_new_span(span_group);
        lv_span_set_text(span_group_sub_level, sub_level_str);
        lv_style_set_text_color(&span_group_sub_level->style, lv_color_white());
        lv_style_set_text_font(&span_group_sub_level->style, FONT_SLIDER_SUB_LEVEL);

        SliderOption.span_group = span_group;
        SliderOption.span_group_level = span_group_level;
        SliderOption.span_group_sub_level = span_group_sub_level;

        // 切换显示label或span
        switch_label_obj(sub_level_str, span_group, level_label, group);

        // 创建TCM组件
        lv_obj_t *tcm_obj = comp_tcm_init(slider);
        lv_obj_add_flag(tcm_obj, LV_OBJ_FLAG_HIDDEN);
        if (config->create_group_area) {
            lv_obj_clear_flag(tcm_obj, LV_OBJ_FLAG_CHECKABLE);
        }
        SliderOption.tcm_obj = tcm_obj;
        update_tcm_obj(tcm_obj, group);
    }

    // 为home类型创建mask
    if (config->create_group_area) {
        lv_obj_t *mask = lv_obj_create(main_container);
        lv_obj_add_flag(mask, LV_OBJ_FLAG_IGNORE_LAYOUT);
        lv_obj_set_size(mask, lv_obj_get_style_width(main_container, 0), lv_obj_get_style_height(main_container, 0));
        lv_obj_add_style(mask, &PageManager.default_style, 0);
        lv_obj_set_style_bg_color(mask, lv_color_black(), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(mask, LV_OPA_50, 0);
        lv_obj_clear_flag(mask, LV_OBJ_FLAG_CLICKABLE);
        lv_obj_add_flag(mask, LV_OBJ_FLAG_HIDDEN);

        if ((Param.ctrl_type == type_flash && group->mode == mode_OFF) ||
            (Param.ctrl_type == type_lamp && group->stylish_lamp_mode == lamp_off)) {
            lv_obj_clear_flag(mask, LV_OBJ_FLAG_HIDDEN);
        }

        SliderOption.mask = mask;
    }

    // 创建聚焦边框
    PageTypeHandle *page = (config->page_type == Page_Home) ? &Pages.page[Page_Home] : &Pages.page[Page_Group_Info];
    border_focused_obj_init(main_container, page, config->focused_flags);

    SliderOption.slider_bg = slider_bg;

    return SliderOption;
}

static void more_grp_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    // LV_LOG("more_grp_cb code %d\n", code);
    if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_ESC) {
                // 创建页面
                pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page, LV_ALIGN_OUT_LEFT_MID,
                              lv_color_black());
                page_click_anim(pos_left, &Pages.page[Page_Multi], anim_slide);
                return;
            } else if (c == LV_KEY_END) {
                // 创建页面
                pm_click_to_page_control_center();
            }
            if (c != LV_KEY_ENTER) {
                return;
            }
        }
        if (Anim.is_finished_anim) {
            pm_creat_page(PageManager.main_page->pos[pos_next].page, PageManager.main_page, LV_ALIGN_OUT_RIGHT_TOP,
                          lv_color_black());
            lv_obj_set_parent(status_bar, PageManager.main_page->obj);
            page_click_anim(pos_right, &Pages.page[Page_Group_Switch], anim_slide);
        }
    }
}

static void more_label_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_VALUE_CHANGED) {
        lv_obj_t *tar_obj = lv_event_get_target(e);
        lv_label_set_text(tar_obj, get_text(text_more));
    }
}

void slider_layout_home(lv_obj_t *parent) {
    // 创建垂直布局obj
    lv_obj_t *v_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_size(v_layout, LV_PCT(100), slider_comp_height * 3 + 10 * 2);
    lv_obj_set_style_bg_opa(v_layout, LV_OPA_TRANSP, 0);
    // // 设置为垂直布局
    // lv_obj_set_flex_flow(v_layout, LV_FLEX_FLOW_COLUMN);
    // 将垂直布局容器居中
    // lv_obj_center(v_layout);
    // lv_obj_set_align(v_layout, LV_ALIGN_TOP_MID);
    // lv_obj_align(v_layout, LV_ALIGN_TOP_MID, 0, 56);
    // lv_obj_set_style_pad_hor(v_layout, slider_layout_padding, 0);
    lv_obj_set_scrollbar_mode(v_layout, LV_SCROLLBAR_MODE_OFF);
    // 设置布局属性
    // 设置子对象间的间距
    lv_obj_set_style_pad_gap(v_layout, 10, 0);
    // 将子对象居中
    // lv_obj_set_flex_align(v_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    // 设置滚动方向，便于焦点判断
    lv_obj_set_scroll_dir(v_layout, LV_DIR_VER);

    for (int i = 0; i < GroupItemCount; ++i) {
        Param.group[i].slider_home = comp_group_slider_init(v_layout, &Param.group[i]);
    }

    lv_obj_t *more_grp = lv_layout_custom_create(v_layout, LV_FLEX_FLOW_ROW);
    lv_obj_set_size(more_grp, LV_PCT(100), slider_comp_height);
    lv_obj_set_style_bg_color(more_grp, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_set_style_radius(more_grp, RADIUS_DEFAULT, 0);
    lv_obj_set_style_pad_gap(more_grp, 4, 0);
    lv_obj_set_flex_align(more_grp, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_event_cb(more_grp, more_grp_cb, LV_EVENT_ALL, NULL);
    // 聚焦边框创建
    border_focused_obj_init(more_grp, &Pages.page[Page_Home], Focused_Scroll_Parent | Focused_Opr_Parent);

    lv_obj_t *more_grp_ico = lv_img_custom_create(more_grp, &icon_more_groups);
    // lv_obj_set_style_border_width(more_grp_ico,1,0);
    lv_obj_t *label = lv_label_custom_create(more_grp, get_text(text_more), FONT_SLIDER_LEVEL, lv_color_white(),
                                             LV_TEXT_ALIGN_CENTER);
    lv_obj_add_event_cb(label, more_label_cb, LV_EVENT_VALUE_CHANGED, NULL);
    Param.more_label_obj = label;
    // lv_obj_set_style_border_width(label,1,0);
}

void slider_grp_page_utils_func(lv_obj_t *parent, GroupStruct *grp_name, SliderObjStruct *slider_flash_level,
                                SliderObjStruct *slider_lamp_level, SliderObjStruct *slider_zoom_level) {
    *slider_flash_level = comp_option_slider_init(parent, &icon_flash, grp_name);
    *slider_lamp_level = comp_option_slider_init(parent, &icon_lamp, grp_name);
    *slider_zoom_level = comp_option_slider_init(parent, &icon_zoom, grp_name);
}

lv_obj_t *slider_layout_grp_page(lv_obj_t *page, GroupStruct *grp) {
    // 创建垂直布局obj
    lv_obj_t *v_layout = lv_layout_custom_create(page, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_size(v_layout, LV_PCT(100), opt_slider_layout_height);
    lv_obj_set_style_bg_opa(v_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_style_pad_gap(v_layout, slider_layout_option_padding, 0);
    // // 设置为垂直布局
    // lv_obj_set_flex_flow(v_layout, LV_FLEX_FLOW_COLUMN);
    // 将垂直布局容器居中
    // lv_obj_center(v_layout);
    // lv_obj_set_align(v_layout, LV_ALIGN_TOP_MID);
    lv_obj_align(v_layout, LV_ALIGN_TOP_MID, 0, 12);
    // lv_obj_set_style_pad_hor(v_layout, slider_layout_padding, 0);
    lv_obj_set_scrollbar_mode(v_layout, LV_SCROLLBAR_MODE_OFF);

    // 设置布局属性
    // 设置子对象间的间距
    // lv_obj_set_style_pad_gap(v_layout, 20, 0);
    // 将子对象居中
    // lv_obj_set_flex_align(v_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    slider_grp_page_utils_func(v_layout, grp, &grp->slider_option_flash_level,
                               &grp->slider_option_lamp_level, &grp->slider_option_zoom_level);
    grp->slider_grp_layout = v_layout;
    if (grp->mode == mode_OFF) {
        lv_obj_add_flag(v_layout, LV_OBJ_FLAG_HIDDEN);
    }
    return v_layout;
}
