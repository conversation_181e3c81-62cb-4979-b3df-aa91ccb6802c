//
// Created by <PERSON><PERSON> on 2024/7/2.
//

#include "comp_slider.h"
#include "comp_border_focused.h"
#include "utils.h"
#include "view_model.h"
#include "comp_status_bar.h"
#include "multi_language.h"
#include "comp_grp_switch.h"

//  实机滑动条存在死机问题
//  疑似label导致,存在部分时候label的值不改变,仅改变滑条宽度,此时点击其他组别必定死机,
//  TTL下反复滑动极易复现
//  确认为label的str动态内存分配的问题

// TODO 手柄在动画过程中连续点击效果异常
//  在主页点击进入详情页的过程中反复点击左手柄
//  考虑开始动画时禁用手柄点击flag,动画结束后再激活,可避免动画打断

// 滑动条容器左右边距
const uint8_t slider_layout_padding = 18;
const uint8_t slider_layout_option_padding = 6;
const lv_coord_t grp_name_area_width = 54;
const lv_coord_t slider_comp_height = 100;
const lv_coord_t slider_comp_option_height = 86;
const lv_coord_t opt_slider_layout_height = slider_comp_option_height * 3 + slider_layout_option_padding * 2;

static lv_point_t initial_point;
static bool is_dragging = false;

static lv_point_t last_point;

static SliderEventEnumType slider_event;
static bool is_short_clicked = false;
static uint32_t count_offset_0;
// 局部非静态数组未初始化为随机数，全局数组为0
static uint8_t pressing_count[5], pressing = 0;

// 更新tcm_obj状态
void update_tcm_obj(lv_obj_t *tcm_obj, GroupStruct *grp) {
    if (grp->mode == mode_M && Setting.values[setting_tcm] && Param.ctrl_type == type_flash &&
        grp->group_name <= group_C) {
        lv_obj_clear_flag(tcm_obj, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(tcm_obj, LV_OBJ_FLAG_HIDDEN);
    }
}

// TCM组件
lv_obj_t *comp_tcm_init(lv_obj_t *parent) {
    lv_obj_t *tcm_obj = lv_obj_custom_trans_create(parent, 46, 24);
    lv_obj_set_style_border_width(tcm_obj, 2, 0);
    lv_obj_set_style_border_color(tcm_obj, lv_color_white(), 0);
    lv_obj_set_style_radius(tcm_obj, 6, 0);
    lv_obj_align_to(tcm_obj, parent, LV_ALIGN_OUT_RIGHT_TOP, -(46 + 11 + 4), 8 + 2);
    lv_obj_add_flag(tcm_obj, LV_OBJ_FLAG_IGNORE_LAYOUT);
    lv_obj_clear_flag(tcm_obj, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_t *tcm_label = lv_label_custom_create(tcm_obj, "TCM", FONT_TCM, lv_color_white(), LV_TEXT_ALIGN_CENTER);
    lv_obj_center(tcm_label);
    return tcm_obj;
}

// 滑动条与页面滚动冲突事件处理
void slider_parent_scrollable_handle(SliderTypeEnum slider_type, bool flag, lv_obj_t *slider_bg) {
    if (slider_type == slider_home) {
        if (flag) {
            lv_obj_add_flag(slider_bg->parent->parent, LV_OBJ_FLAG_SCROLLABLE);
        } else {
            lv_obj_clear_flag(slider_bg->parent->parent, LV_OBJ_FLAG_SCROLLABLE);
        }
    } else if (slider_type == slider_option) {
        if (flag) {
            lv_obj_add_flag(Param.grp_tile_view, LV_OBJ_FLAG_SCROLLABLE);
        } else {
            lv_obj_clear_flag(Param.grp_tile_view, LV_OBJ_FLAG_SCROLLABLE);
        }
    }
}

// 切换span_group和label
void switch_label_obj(char *sub_level_str, lv_obj_t *span_group, lv_obj_t *level_label, GroupStruct *group) {
    if (strcmp(sub_level_str, "") == 0 || (Setting.values[setting_tcm] && group->group_name <= group_C)) {
        lv_obj_add_flag(span_group, LV_OBJ_FLAG_HIDDEN);
        lv_obj_clear_flag(level_label, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(level_label, LV_OBJ_FLAG_HIDDEN);
        lv_obj_clear_flag(span_group, LV_OBJ_FLAG_HIDDEN);
    }
}

// 切换模式label和造型灯img
void switch_type_obj(lv_obj_t *mode_label, lv_obj_t *lamp_img) {
    if (Param.ctrl_type == type_flash) {
        lv_obj_clear_flag(mode_label, LV_OBJ_FLAG_HIDDEN);
        lv_obj_add_flag(lamp_img, LV_OBJ_FLAG_HIDDEN);
    } else if (Param.ctrl_type == type_lamp) {
        lv_obj_clear_flag(lamp_img, LV_OBJ_FLAG_HIDDEN);
        lv_obj_add_flag(mode_label, LV_OBJ_FLAG_HIDDEN);
    }
}

// 滑动条事件处理
static void slider_event_handler(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_obj_t *current_tar_obj = lv_event_get_current_target(e);
    lv_obj_t *slider;
    lv_obj_t *slider_bg;
    if (tar_obj == current_tar_obj) {
        slider_bg = tar_obj;
        slider = lv_obj_get_child(tar_obj, 0);
    } else {
        slider_bg = lv_obj_get_parent(tar_obj);
        slider = lv_obj_get_child(slider_bg, 0);
    }
    // lv_obj_t *tar_obj_parent = lv_obj_get_parent(tar_obj);
    lv_event_code_t code = lv_event_get_code(e);

    // 滑动检测阈值
    lv_coord_t swipe_detection_threshold = 10;
    lv_coord_t slider_width = lv_obj_get_width(slider_bg);
    // lv_coord_t step_px = 0;
    SliderStruct *slider_struct;
    // 防止slider_struct->level_max指向为空导致的死机
    if (lv_obj_get_user_data(slider_bg) != NULL) {
        slider_struct = lv_obj_get_user_data(slider_bg);
        // if (slider_struct->level_max != 0) {
        //     step_px = slider_width / slider_struct->level_max;
        // }
    } else {
        return;
    }

    static bool last_point_has_data = false;

    uint8_t *flash_level = get_flash_level(slider_struct->group);
    // LV_LOG("flash_level; %d\n", *flash_level);
    // LV_LOG("group; %s\n", group_text[slider_struct->group->group_name]);
    if (code == LV_EVENT_VALUE_CHANGED) {
        // LV_LOG("slider_struct->level_type %d\n", slider_struct->level_type);
        // char *level_str = malloc(6 * sizeof(char));
        // char *sub_level_str = NULL;
        // 预留足够的空间存储结果，最大为 "+0.9\0"
        // 预留足够的空间存储结果，最大为 "200mm\0"
        char level_str[20], sub_level_str[6];
        lv_coord_t slider_level_width = 0;
        if (slider_struct->level_type == level_lamp) {
            lv_color_t label_color = lv_color_white();
            if (slider_struct->group->stylish_lamp_mode == lamp_off) {
                slider_level_width = 0;
                strncpy(level_str, get_text(text_off), sizeof(level_str));
                label_color = lv_color_hex(OFF_Color);
                if (Setting.lamp_main_switch) {
                    bool lamp_main_switch_check = false;
                    for (int i = 0; i < GroupItemCount; ++i) {
                        if (Param.group[i].is_added_to_list && Param.group[i].stylish_lamp_mode != lamp_off) {
                            lamp_main_switch_check = true;
                        }
                    }
                    if (!lamp_main_switch_check) {
                        Setting.lamp_main_switch = false;
                        update_status_bar();
                    }
                }
            } else {
                if (!Setting.lamp_main_switch) {
                    Setting.lamp_main_switch = true;
                    // if (PageManager.main_page == &Pages.page[Page_Home]) {
                    update_status_bar();
                    // }
                }
#if LAMP_PROP
                if (slider_struct->group->stylish_lamp_mode == lamp_prop) {
                    slider_level_width = slider_width;
                    strncpy(level_str, "PROP", sizeof(level_str));
                } else
#endif
                if (slider_struct->group->stylish_lamp_mode == lamp_manual) {
                    // 获取滑动条对应能级宽度
                    slider_level_width = cal_slider_width_by_level(slider_width,
                                                                   slider_struct->group->stylish_lamp_level,
                                                                   MAX_LAMP_LEVEL);
                    sprintf(level_str, "%d0%%", slider_struct->group->stylish_lamp_level);
                }
            }
            if (slider_struct->slider_type == slider_home) {
                switch_label_obj("", slider_struct->group->slider_home.span_group,
                                 slider_struct->group->slider_home.level_label, slider_struct->group);
                lv_label_set_text(slider_struct->group->slider_home.level_label, level_str);
                lv_obj_set_style_text_color(slider_struct->group->slider_home.level_label, label_color, 0);
                if (slider_struct->group->stylish_lamp_mode == lamp_off) {
                    lv_obj_clear_flag(slider_struct->group->slider_home.mask, LV_OBJ_FLAG_HIDDEN);
                } else {
                    lv_obj_add_flag(slider_struct->group->slider_home.mask, LV_OBJ_FLAG_HIDDEN);
                }
                // 更新tcm obj，避免ctrl_type切换未更新tcm_obj状态
                update_tcm_obj(slider_struct->group->slider_home.tcm_obj, slider_struct->group);
            } else if (slider_struct->slider_type == slider_option) {
                lv_label_set_text(slider_struct->group->slider_option_lamp_level.level_label, level_str);
                lv_obj_set_style_text_color(slider_struct->group->slider_option_lamp_level.level_label, label_color, 0);
            }
            // 设置滑动条宽度
            lv_obj_set_width(slider, slider_level_width);
            set_beep_lamp_level();
            // free_and_clear(level_str);
        } else if (slider_struct->level_type == level_zoom) {
            if (slider_struct->group->is_auto_zoom) {
                slider_level_width = slider_width;
                strncpy(level_str, get_text(text_auto), sizeof(level_str));
            } else {
                // 获取滑动条对应能级宽度
                slider_level_width = cal_slider_width_by_level(slider_width, slider_struct->group->zoom_level,
                                                               MAX_ZOOM_LEVEL);

                // level_str = zoom_level_text[slider_struct->group->zoom_level];
                strncpy(level_str, zoom_level_text[slider_struct->group->zoom_level], sizeof(level_str));
#if ProductModel == QZ_F
                if (Setting.values[setting_zoom_disp] == zoom_aps) {
                    strncpy(level_str, zoom_aps_level_text[slider_struct->group->zoom_level], sizeof(level_str));
                }
#endif
            }
            // 设置滑动条宽度
            lv_obj_set_width(slider, slider_level_width);
            lv_label_set_text(slider_struct->group->slider_option_zoom_level.level_label, level_str);
            set_zoom_level(slider_struct->group->group_name);
        } else {
            // 获取滑动条对应能级宽度
            slider_level_width = cal_slider_width_by_level(slider_width, *flash_level,
                                                           get_flash_level_max(slider_struct->group));

            if (slider_struct->group->mode == mode_M) {
                // level_str = get_str_flash_level_M(*flash_level);
                if (Setting.values[setting_tcm] && slider_struct->group->group_name <= group_C) {
                    if (slider_struct->group->flash_level_M_tcm > 0) {
                        snprintf(level_str, sizeof(level_str), "+%d.%d", slider_struct->group->flash_level_M_tcm / 10,
                                 slider_struct->group->flash_level_M_tcm % 10);
                    } else if (slider_struct->group->flash_level_M_tcm < 0) {
                        snprintf(level_str, sizeof(level_str), "-%d.%d", -slider_struct->group->flash_level_M_tcm / 10,
                                 -slider_struct->group->flash_level_M_tcm % 10);
                    } else {
                        snprintf(level_str, sizeof(level_str), "0.0");
                    }
                    slider_level_width = cal_slider_width_by_level(slider_width,
                                                                   int8_2_uint8(slider_struct->group->flash_level_M_tcm,
                                                                                MAX_FLASH_LEVEL_M_TCM),
                                                                   MAX_FLASH_LEVEL_M_TCM);
                } else {
                    // Setting.roller_values[roller_min_power]前三项为分数
                    if (Setting.roller_values[roller_min_power] / MAX_FLASH_LEVEL_INDEX == 0) {
                        strncpy(level_str, get_str_flash_level_M(*flash_level), sizeof(level_str));
                        get_str_flash_sub_level_M(*flash_level, sub_level_str, sizeof(sub_level_str));
                    } else {
                        strncpy(level_str, get_decimals_str_flash_level_M(*flash_level), sizeof(level_str));
                        sub_level_str[0] = '\0';
                    }
                }
            } else if (slider_struct->group->mode == mode_TTL) {
                // level_str = get_str_flash_level_TTL(*flash_level);
                strncpy(level_str, get_str_flash_level_TTL(*flash_level), sizeof(level_str));
                sub_level_str[0] = '\0';
            } else {
                // level_str = "----";
                strncpy(level_str, "----", sizeof(level_str));
                sub_level_str[0] = '\0';
            }
            // 确保以 '\0' 结尾
            level_str[sizeof(level_str) - 1] = '\0';

            LV_LOG("--flash_level %d\n", *flash_level);
            // 设置滑动条宽度
            lv_obj_set_width(slider, slider_level_width);

            if (slider_struct->slider_type == slider_home) {
                mode_label_color_handle(slider_struct->group->slider_home.level_label,
                                        slider_struct->group->mode);
                lv_label_set_text(slider_struct->group->slider_home.level_label, level_str);
                lv_span_set_text(slider_struct->group->slider_home.span_group_level, level_str);
                lv_span_set_text(slider_struct->group->slider_home.span_group_sub_level, sub_level_str);
                switch_label_obj(sub_level_str, slider_struct->group->slider_home.span_group,
                                 slider_struct->group->slider_home.level_label, slider_struct->group);
                // 更新tcm obj
                update_tcm_obj(slider_struct->group->slider_home.tcm_obj, slider_struct->group);

                mode_label_color_handle(slider_struct->group->slider_home.mode_label, slider_struct->group->mode);
                lv_label_set_text(slider_struct->group->slider_home.mode_label,
                                  mode_text[slider_struct->group->mode]);
                if (slider_struct->group->mode == mode_OFF) {
                    lv_obj_clear_flag(slider_struct->group->slider_home.mask, LV_OBJ_FLAG_HIDDEN);
                } else {
                    lv_obj_add_flag(slider_struct->group->slider_home.mask, LV_OBJ_FLAG_HIDDEN);
                }
            } else if (slider_struct->slider_type == slider_option) {
                mode_label_color_handle(slider_struct->group->slider_option_flash_level.level_label,
                                        slider_struct->group->mode);
                lv_label_set_text(slider_struct->group->slider_option_flash_level.level_label, level_str);
                lv_span_set_text(slider_struct->group->slider_option_flash_level.span_group_level, level_str);
                lv_span_set_text(slider_struct->group->slider_option_flash_level.span_group_sub_level, sub_level_str);
                switch_label_obj(sub_level_str, slider_struct->group->slider_option_flash_level.span_group,
                                 slider_struct->group->slider_option_flash_level.level_label, slider_struct->group);
                // 更新tcm obj
                update_tcm_obj(slider_struct->group->slider_option_flash_level.tcm_obj, slider_struct->group);
            }
            set_flash_mode_level();
        }
    } else if (code == LV_EVENT_LONG_PRESSED) {
        // static bool is_long_pressed = false;
        // if (!is_long_pressed){
        //     lv_group_remove_all_objs(Pages.Page_Home.indev_group);
        //     is_long_pressed = true;
        // } else{
        //     for (int i = 0; i < GROUP_OBJ_MAX; ++i) {
        //         if (Pages.Page_Home.group_obj[i] == NULL) {
        //             break;
        //         } else {
        //             lv_group_add_obj(Pages.Page_Home.indev_group, Pages.Page_Home.group_obj[i]);
        //         }
        //     }
        //     // lv_indev_set_group(lv_win32_encoder_device_object, Pages.Page_Home.indev_group);
        //     is_long_pressed = false;
        // }
        LV_LOG("---LV_EVENT_LONG_PRESSED---\n");
        // pm_creat_page(PageManager.main_page->bottom.page, PageManager.main_page, LV_ALIGN_OUT_BOTTOM_MID,
        //               lv_color_hex(0x000000));
    } else if (code == LV_EVENT_PRESSED) {
        // 按下
        // 记录初始触控位置
        lv_indev_t *indev = lv_indev_get_act();
        if (indev) {
            lv_indev_get_point(indev, &initial_point);
        }
        is_dragging = false;
        // LV_LOG("%d\n", lv_obj_get_height(slider));
        // LV_LOG("%d\n", slider_width);
        // LV_LOG("%d\n", step_px);
        // lv_obj_set_scrollbar_mode(slider_bg->parent->parent,LV_SCROLLBAR_MODE_OFF);
    } else if (code == LV_EVENT_PRESSING) {
        // lv_area_t coords;
        // lv_obj_get_coords(slider_bg, &coords);  // 获取对象的坐标区域

        // lv_coord_t x1 = coords.x1;  // 左上角的X坐标
        // lv_coord_t y1 = coords.y1;  // 左上角的Y坐标
        // lv_coord_t x2 = coords.x2;  // 右下角的X坐标
        // lv_coord_t y2 = coords.y2;  // 右下角的Y坐标
        // LV_LOG("x1: %d, y1: %d, x2: %d, y2: %d\n", x1, y1, x2, y2);
        // 按住
        // 检测滑动
        lv_point_t current_point;
        lv_indev_t *indev = lv_indev_get_act();
        if (indev) {
            lv_indev_get_point(indev, &current_point);
            // x或y移动超过swipe_detection_threshold像素时视为滑动
            if (LV_ABS(current_point.x - initial_point.x) > swipe_detection_threshold ||
                LV_ABS(current_point.y - initial_point.y) > swipe_detection_threshold) {
                is_dragging = true;
            }
        }
        if (!last_point_has_data) {
            last_point = current_point;
            last_point_has_data = true;
            return;
        }
        lv_coord_t offset_x = (lv_coord_t) (last_point.x - current_point.x);
        lv_coord_t offset_y = (lv_coord_t) (last_point.y - current_point.y);
        // LV_LOG("offset_x: %d\n", offset_x);
        // lv_coord_t target_width = lv_obj_get_width(slider);
        // LV_LOG("target_width %d\n", target_width);
        // 滑动灵敏度SwipeSensitivity(-offset_x) / 2
        // 滑动值
        // lv_coord_t swipe_value = 0;
        // 过滤移动坐标大于40的值
        // uint8_t offset_filter_threshold = 40;
        if (!slider_event) {
            if (LV_ABS(offset_y) > 0 && LV_ABS(offset_y) > LV_ABS(offset_x)) {
                slider_event = slider_event_list;
            } else if (LV_ABS(offset_x) > 0 && LV_ABS(offset_x) > LV_ABS(offset_y)) {
                slider_event = slider_event_slider;
            }
        }
        // LV_LOG("slider_event: %d\n", slider_event);
        if (slider_event == slider_event_list) {
            // LV_LOG("slider_event == slider_event_list\n");
            is_short_clicked = false;
            slider_parent_scrollable_handle(slider_struct->slider_type, true, slider_bg);
            // lv_obj_add_flag(slider_bg->parent->parent, LV_OBJ_FLAG_SCROLLABLE);
        } else if (slider_event == slider_event_slider) {
            // LV_LOG("slider_event == slider_event_slider\n");
            is_short_clicked = false;
            slider_parent_scrollable_handle(slider_struct->slider_type, false, slider_bg);
            // 调整代码逻辑，将所有改值的部分放入slider_event_slider内，避免异常改值但界面又未显示
            // lv_obj_clear_flag(slider_bg->parent->parent, LV_OBJ_FLAG_SCROLLABLE);
            // lv_obj_set_width(slider, set_width);
            // uint8_t count_num = 5;
            // uint8_t steps[] = {0, 1, 2, 3, 3};
            // uint16_t thresholds[] = {0, 20, 45, 70, 999};

            // 灵敏度调节
            uint8_t count_num, steps[5];
            uint16_t thresholds[5];
            if (slider_struct->level_type == level_zoom) {
                count_num = 5 + 1;
                uint8_t zoom_steps[] = {0, 1, 3, 5, 5};
                uint16_t zoom_thresholds[] = {1, 20, 45, 999, 999};
                memcpy(steps, zoom_steps, sizeof(steps));
                memcpy(thresholds, zoom_thresholds, sizeof(thresholds));
            } else if (slider_struct->level_type == level_lamp) {
                count_num = 5 + 1;
                uint8_t zoom_steps[] = {0, 1, 3, 5, 5};
                uint16_t zoom_thresholds[] = {1, 20, 45, 999, 999};
                memcpy(steps, zoom_steps, sizeof(steps));
                memcpy(thresholds, zoom_thresholds, sizeof(thresholds));
            } else {
                if (slider_struct->group->mode == mode_M) {
                    if (Setting.values[setting_tcm]) {
                        count_num = 1 + 1;
                        uint8_t M_steps[] = {0, 1, 5, 10, 20};
                        uint16_t M_thresholds[] = {1, 8, 20, 30, 999};
                        memcpy(steps, M_steps, sizeof(steps));
                        memcpy(thresholds, M_thresholds, sizeof(thresholds));
                    } else {
                        count_num = 1 + 1;
                        uint8_t M_steps[] = {0, 1, 5, 12, 30};
                        uint16_t M_thresholds[] = {1, 8, 20, 40, 999};
                        memcpy(steps, M_steps, sizeof(steps));
                        memcpy(thresholds, M_thresholds, sizeof(thresholds));
                    }
                } else if (slider_struct->group->mode == mode_TTL) {
                    count_num = 4 + 1;
                    uint8_t M_steps[] = {0, 1, 3, 10, 10};
                    uint16_t M_thresholds[] = {1, 20, 30, 999, 999};
                    memcpy(steps, M_steps, sizeof(steps));
                    memcpy(thresholds, M_thresholds, sizeof(thresholds));
                }
            }

            for (int i = 5 - 1; i >= 0; i--) {
                if (pressing_count[i] > 0) {
                    pressing_count[i]--;
                    pressing = (pressing_count[i] == 0 && i > 0) ? 1 : 0;
                    break;
                }
            }

            // 然后检查 offset_x 并更新 pressing_count
            uint8_t step = 0;
            for (int i = 0; i < 5; ++i) {
                if (LV_ABS(offset_x) <= thresholds[i]) {
                    step = steps[i];
                    if (!pressing_count[i]) {
                        // 清零所有较小索引的 pressing_count
                        for (int j = 0; j <= i; j++) {
                            pressing_count[j] = 0;
                        }
                        pressing_count[i] = count_num;
                        pressing = (i > 0) ? 1 : 0;
                    }
                    break;
                }
            }

            if (pressing) {
                LV_LOG("offset_x: %d, step: %d, pressing: %d\n", -offset_x, step, pressing);
                if (slider_struct->level_type == level_lamp && slider_struct->group->stylish_lamp_mode == lamp_manual) {
                    slider_struct->group->stylish_lamp_level = level_adjust_handle(-offset_x,
                                                                                   slider_struct->group->stylish_lamp_level,
                                                                                   MIN_LAMP_LEVEL, MAX_LAMP_LEVEL,
                                                                                   step);
                } else if (slider_struct->level_type == level_zoom && !slider_struct->group->is_auto_zoom) {
                    slider_struct->group->zoom_level =
                            level_adjust_handle(-offset_x, slider_struct->group->zoom_level, 0, MAX_ZOOM_LEVEL, step);
                } else if (slider_struct->level_type == level_flash) {
                    if (slider_struct->group->mode == mode_M && Setting.values[setting_tcm] &&
                        slider_struct->group->group_name <= group_C) {
                        int8_t new_tcm = slider_struct->group->flash_level_M_tcm;
                        new_tcm = (int8_t) level_adjust_handle(-offset_x, new_tcm, MIN_FLASH_LEVEL_M_TCM_INT_8,
                                                               MAX_FLASH_LEVEL_M_TCM_INT_8, step);
                        slider_struct->group->flash_level_M_tcm = new_tcm;
                    } else {
                        *flash_level = level_adjust_handle(-offset_x, *flash_level,
                                                           0, get_flash_level_max(slider_struct->group), step);
                    }
                    if (slider_struct->group->mode == mode_M && !Setting.values[setting_tcm] &&
                        slider_struct->group->group_name <= group_C) {
                        // 应用0.3步进处理
                        *flash_level = level_step_0_3_handle(-offset_x, *flash_level);
                    }
                }
                lv_event_send(slider, LV_EVENT_VALUE_CHANGED, NULL);
            }
        } else {
            LV_LOG("is_short_clicked\n");
            is_short_clicked = true;
        }
        last_point = current_point;
        // short_click灵敏度
        // 连续触发5次pressing且未移动也不允许进入short_click
        if (!offset_x) {
            count_offset_0++;
            if (count_offset_0 > 20) {
                is_short_clicked = false;
            }
        }

        LV_LOG("------------------offset %d\n", offset_x);
    } else if (code == LV_EVENT_RELEASED) {
        slider_parent_scrollable_handle(slider_struct->slider_type, true, slider_bg);
        // lv_obj_add_flag(slider_bg->parent->parent, LV_OBJ_FLAG_SCROLLABLE);
        // lv_obj_set_scrollbar_mode(slider_bg->parent->parent, LV_SCROLLBAR_MODE_OFF);
        last_point_has_data = false;
        slider_event = slider_event_none;
        last_point.x = 0;
        last_point.y = 0;
        count_offset_0 = 0;
        memset(pressing_count, 0, sizeof(pressing_count));
        // 松手
        if (is_dragging) {
            LV_LOG("Slider moved\n");
        } else {
            LV_LOG("Clicked\n");
        }
    } else if (code == LV_EVENT_SHORT_CLICKED) {
        uint8_t param = (uintptr_t) lv_event_get_param(e);
        if (param == 0xFF) {
            LV_LOG("param == true\n");
            is_short_clicked = true;
        }
        LV_LOG("Anim.is_finished_anim: %d\n", Anim.is_finished_anim);
        if (is_short_clicked && Anim.is_finished_anim) {
            LV_LOG("SHORT_CLICKED\n");
            is_short_clicked = false;
            if (slider_struct->slider_type == slider_home) {
                // 创建页面
                pm_creat_page(PageManager.main_page->pos[pos_bottom].page, PageManager.main_page,
                              LV_ALIGN_OUT_RIGHT_MID,
                              lv_color_black());
                if (slider_struct->group->tile_gpr != NULL) {
                    // 不更新会导致set_tile无效，始终跳id = 0的tile
                    lv_obj_update_layout(Param.grp_tile_view);
                    lv_obj_set_tile(Param.grp_tile_view, (lv_obj_t *) slider_struct->group->tile_gpr, LV_ANIM_OFF);
                    LV_LOG("NOT NULL\n");
                    LV_LOG("grp_name %s\n", group_text[slider_struct->group->group_name]);
                    if (slider_struct->group->tile_gpr == Param.group[group_A].tile_gpr) {
                        LV_LOG("grp_name %s\n", group_text[slider_struct->group->group_name]);
                    }
                }
                // 详情页隐藏状态栏
                // lv_obj_set_parent(status_bar, PageManager.main_page->obj);
                page_click_anim(pos_right, &Pages.page[Page_Group_Info], anim_slide);
            } else if (slider_struct->slider_type == slider_option) {
                // 切换自动模式
                if (slider_struct->level_type == level_zoom) {
                    slider_struct->group->is_auto_zoom = !slider_struct->group->is_auto_zoom;
                } else if (slider_struct->level_type == level_lamp) {
                    lamp_mode_switch_handle(slider_struct->group);
                }
                lv_event_send(slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
            }
        }
    } else if (code == LV_EVENT_KEY) {
        char c = *((char *) lv_event_get_param(e));
        // LV_LOG("c %d\n", c);
        if (c == LV_KEY_LEFT) {
            if (slider_struct->level_type == level_lamp) {
                slider_struct->group->stylish_lamp_level = level_adjust_handle(1,
                                                                               slider_struct->group->stylish_lamp_level,
                                                                               MIN_LAMP_LEVEL, MAX_LAMP_LEVEL, 1);
            } else if (slider_struct->level_type == level_zoom) {
                if (!slider_struct->group->is_auto_zoom) {
                    slider_struct->group->zoom_level =
                            level_adjust_handle(1, slider_struct->group->zoom_level, 0, MAX_ZOOM_LEVEL, 1);
                }
            } else {
                if (Setting.values[setting_tcm] && slider_struct->group->group_name <= group_C) {
                    int8_t new_tcm = slider_struct->group->flash_level_M_tcm;
                    new_tcm = (int8_t) level_adjust_handle(1, new_tcm, MIN_FLASH_LEVEL_M_TCM_INT_8,
                                                           MAX_FLASH_LEVEL_M_TCM_INT_8, 1);
                    slider_struct->group->flash_level_M_tcm = new_tcm;
                } else {
                    *flash_level = level_adjust_handle(1, *flash_level, 0,
                                                       get_flash_level_max(slider_struct->group), 1);
                    if (slider_struct->group->mode == mode_M) {
                        // 应用0.3步进处理
                        *flash_level = level_step_0_3_handle(1, *flash_level);
                    }
                }
            }
        } else if (c == LV_KEY_RIGHT) {
            if (slider_struct->level_type == level_lamp) {
                slider_struct->group->stylish_lamp_level = level_adjust_handle(-1,
                                                                               slider_struct->group->stylish_lamp_level,
                                                                               MIN_LAMP_LEVEL, MAX_LAMP_LEVEL, 1);
            } else if (slider_struct->level_type == level_zoom) {
                if (!slider_struct->group->is_auto_zoom) {
                    slider_struct->group->zoom_level =
                            level_adjust_handle(-1, slider_struct->group->zoom_level, 0, MAX_ZOOM_LEVEL, 1);
                }
            } else {
                if (Setting.values[setting_tcm] && slider_struct->group->group_name <= group_C) {
                    int8_t new_tcm = slider_struct->group->flash_level_M_tcm;
                    new_tcm = (int8_t) level_adjust_handle(-1, new_tcm, MIN_FLASH_LEVEL_M_TCM_INT_8,
                                                           MAX_FLASH_LEVEL_M_TCM_INT_8, 1);
                    slider_struct->group->flash_level_M_tcm = new_tcm;
                } else {
                    *flash_level = level_adjust_handle(-1, *flash_level, 0,
                                                       get_flash_level_max(slider_struct->group), 1);
                    if (slider_struct->group->mode == mode_M) {
                        // 应用0.3步进处理
                        *flash_level = level_step_0_3_handle(-1, *flash_level);
                    }
                }
            }
        } else if (c == LV_KEY_ESC) {
            if (PageManager.main_page == &Pages.page[Page_Home]) {
                if (lv_group_get_editing(lv_group_get_default())) {
                    if (Param.ctrl_type == type_flash) {
                        mode_switch_handle(slider_struct->group);
                    } else if (Param.ctrl_type == type_lamp) {
                        lamp_mode_switch_handle(slider_struct->group);
                    }
                    lv_event_send(slider, LV_EVENT_VALUE_CHANGED, NULL);
                } else {
                    // 创建页面
                    pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page,
                                  LV_ALIGN_OUT_LEFT_MID,
                                  lv_color_black());
                    page_click_anim(pos_left, &Pages.page[Page_Multi], anim_slide);
                }
            } else if (PageManager.main_page == &Pages.page[Page_Group_Info]) {
                if (lv_group_get_editing(lv_group_get_default())) {
                    // 编辑状态下切为AUTO
                    if (slider_struct->level_type == level_zoom || slider_struct->level_type == level_lamp) {
                        uint8_t param = 0xFF;
                        lv_event_send(slider, LV_EVENT_SHORT_CLICKED, (void *) (uintptr_t) param);
                    }
                }
            }
        } else if (c == LV_KEY_HOME) {
            if (PageManager.main_page == &Pages.page[Page_Home]) {
                // // 编辑状态长按进详情页
                // if (lv_group_get_editing(lv_group_get_default())) {
                uint8_t param = 0xFF;
                lv_event_send(slider, LV_EVENT_SHORT_CLICKED, (void *) (uintptr_t) param);
                // } else {
                //     // 创建页面
                //     pm_creat_page(PageManager.main_page->top.page, PageManager.main_page, LV_ALIGN_OUT_TOP_MID,
                //                   lv_color_black());
                //     page_click_anim(pos_top, &Pages.page[Page_Control_Center], anim_overlay);
                // }
            } else if (PageManager.main_page == &Pages.page[Page_Group_Info]) {
                // 创建页面
                pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page, LV_ALIGN_OUT_LEFT_MID,
                              lv_color_black());
                page_click_anim(pos_left, &Pages.page[Page_Home], anim_slide);
            }
        } else if (c == LV_KEY_END) {
            // 创建页面
            pm_click_to_page_control_center();
        }
        if (c != LV_KEY_HOME) {
            lv_event_send(slider, LV_EVENT_VALUE_CHANGED, NULL);
        }
    }
}

// 模式文字obj和造型灯图片obj切换回调
static void type_obj_event_cb(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    TypeObjStruct *type_obj_struct;
    // 防止user_data指向为空导致的死机
    if (lv_obj_get_user_data(tar_obj) != NULL) {
        type_obj_struct = lv_obj_get_user_data(tar_obj);
    } else {
        return;
    }
    switch_type_obj(type_obj_struct->mode_label, type_obj_struct->lamp_img);
}

SliderObjStruct comp_group_slider_init(lv_obj_t *parent_layout, GroupStruct *group) {
    SliderObjStruct SliderOption;
    char *grp_str = group_text[group->group_name];
    char *mode_str = mode_text[group->mode];
    uint8_t level_max = get_flash_level_max(group);
    // char *level_str;
    char level_str[8], sub_level_str[6];
    if (Param.ctrl_type == type_lamp) {
        if (group->stylish_lamp_mode == lamp_off) {
            strncpy(level_str, get_text(text_off), sizeof(level_str));
        }
#if LAMP_PROP
            else if (group->stylish_lamp_mode == lamp_prop) {
                strncpy(level_str, "PROP", sizeof(level_str));
            }
#endif
        else if (group->stylish_lamp_mode == lamp_manual) {
            sprintf(level_str, "%d0%%", group->stylish_lamp_level);
        }
        sub_level_str[0] = '\0';
        level_max = MAX_LAMP_LEVEL;
    } else {
        if (group->mode == mode_M) {
            // strdup令其可以free，以避免最后统一free时导致的未定义崩溃
            // level_str = strdup(get_str_flash_level_M(flash_level));
            // level_str = get_str_flash_level_M(group->flash_level_M);
            if (Setting.values[setting_tcm] && group->group_name <= group_C) {
                if (group->flash_level_M_tcm > 0) {
                    snprintf(level_str, sizeof(level_str), "+%d.%d", group->flash_level_M_tcm / 10,
                             group->flash_level_M_tcm % 10);
                } else if (group->flash_level_M_tcm < 0) {
                    snprintf(level_str, sizeof(level_str), "-%d.%d", -group->flash_level_M_tcm / 10,
                             -group->flash_level_M_tcm % 10);
                } else {
                    snprintf(level_str, sizeof(level_str), "0.0");
                }
            } else {
                // Setting.roller_values[roller_min_power]前三项为分数
                if (Setting.roller_values[roller_min_power] / MAX_FLASH_LEVEL_INDEX == 0) {
                    strncpy(level_str, get_str_flash_level_M(group->flash_level_M), sizeof(level_str));
                    get_str_flash_sub_level_M(group->flash_level_M, sub_level_str, sizeof(sub_level_str));
                } else {
                    strncpy(level_str, get_decimals_str_flash_level_M(group->flash_level_M), sizeof(level_str));
                    sub_level_str[0] = '\0';
                }
            }
        } else if (group->mode == mode_TTL) {
            // level_str = get_str_flash_level_TTL(group->flash_level_TTL);
            strncpy(level_str, get_str_flash_level_TTL(group->flash_level_TTL), sizeof(level_str));
            sub_level_str[0] = '\0';
        } else {
            level_max = 0;
            // level_str = "-----";
            strncpy(level_str, "----", sizeof(level_str));
            sub_level_str[0] = '\0';
        }
    }

    // 确保以 '\0' 结尾
    level_str[sizeof(level_str) - 1] = '\0';

    if (!group->is_added_to_list) {
        return (SliderObjStruct) {0};
    }

    // 创建水平布局obj
    // slider_comp = lv_horizontal_layout_create(parent_layout);
    lv_obj_t *slider_comp = lv_layout_custom_create(parent_layout, LV_FLEX_FLOW_ROW);
    // lv_obj_set_size(slider_comp, LV_PCT(100), LV_SIZE_CONTENT);
    // 设置大小
    lv_obj_set_size(slider_comp, LV_PCT(100), slider_comp_height);
    // 居中对齐
    // lv_obj_align(slider_comp, LV_ALIGN_LEFT_MID, 0, 0);
    // 设置圆角
    lv_obj_set_style_radius(slider_comp, RADIUS_DEFAULT, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(slider_comp, lv_color_hex(Bg_Color_Black), LV_PART_MAIN | LV_STATE_DEFAULT);
    // 去除边框
    lv_obj_set_style_border_width(slider_comp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    // 裁剪超出对象
    lv_obj_set_style_clip_corner(slider_comp, true, LV_PART_MAIN | LV_STATE_DEFAULT);
    // 设置布局属性
    // 设置子对象间的间距
    lv_obj_set_style_pad_gap(slider_comp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    // 将子对象居中
    // lv_obj_set_flex_align(slider_comp, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);

    // if (group->is_added_to_list) {
    //     lv_obj_clear_flag(slider_comp, LV_OBJ_FLAG_HIDDEN);
    // } else {
    //     lv_obj_add_flag(slider_comp, LV_OBJ_FLAG_HIDDEN);
    // }

    // 创建组别显示区域
    lv_obj_t *grp_name_area = lv_layout_custom_create(slider_comp, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_size(grp_name_area, grp_name_area_width, LV_PCT(100));
    lv_obj_set_style_bg_color(grp_name_area, lv_color_hex(Bg_Color_Gray), LV_PART_MAIN | LV_STATE_DEFAULT);
    // 去除边框
    lv_obj_set_style_border_width(grp_name_area, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    // 左对齐
    lv_obj_align(grp_name_area, LV_ALIGN_LEFT_MID, 0, 0);
    // 设置直角
    lv_obj_set_style_radius(grp_name_area, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_flex_align(grp_name_area, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_pad_gap(grp_name_area, POINT_GAP, 0);
    lv_obj_clear_flag(grp_name_area, LV_OBJ_FLAG_SCROLLABLE);

    lv_obj_t *grp_name_label = lv_label_create(grp_name_area);
    // lv_obj_set_style_border_width(grp_name_label, 1, 0);
    lv_label_set_text(grp_name_label, grp_str);
    lv_obj_set_style_text_font(grp_name_label, FONT_HOME_GRP, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(grp_name_label, lv_color_white(), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_center(grp_name_label);

    lv_obj_t *grp_color_point = comp_color_point_init(grp_name_area, group->group_name);
    if (Param.is_grp_color_on) {
        lv_obj_clear_flag(grp_color_point, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(grp_color_point, LV_OBJ_FLAG_HIDDEN);
    }

    lv_obj_t *slider_bg = lv_obj_create(slider_comp);
    lv_obj_set_style_bg_opa(slider_bg, LV_OPA_TRANSP, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(slider_bg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_coord_t slider_width_max = Screen_Width - slider_layout_padding * 2 - grp_name_area_width;
    lv_obj_set_size(slider_bg, slider_width_max, LV_PCT(100));
    lv_obj_clear_flag(slider_bg, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(slider_bg, LV_SCROLLBAR_MODE_OFF);
    lv_obj_add_style(slider_bg, &PageManager.default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_add_event_cb(slider_bg, slider_event_handler, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(slider_bg, free_struct_cb, LV_EVENT_DELETE, NULL);

    // 滑动条结构体，用户数据
    SliderStruct *slider_struct = (SliderStruct *) malloc(sizeof(SliderStruct));
    if (slider_struct == NULL) {
        // 如果分配失败，处理错误
        LV_LOG("slider_struct Memory allocation failed\n");
        return (SliderObjStruct) {0};
    }
    slider_struct->level_max = level_max;
    slider_struct->slider_type = slider_home;
    if (Param.ctrl_type == type_lamp) {
        slider_struct->level_type = level_lamp;
    } else {
        slider_struct->level_type = level_flash;
    }
    slider_struct->group = group;
    lv_obj_set_user_data(slider_bg, slider_struct);

    lv_obj_t *slider = lv_obj_create(slider_bg);
    lv_obj_set_style_border_width(slider, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_coord_t slider_width = 0;
    if (Param.ctrl_type == type_flash) {
        if (group->mode == mode_TTL) {
            slider_width = cal_slider_width_by_level(slider_width_max,
                                                     slider_struct->group->flash_level_TTL,
                                                     MAX_FLASH_LEVEL_TTL);
        } else if (group->mode == mode_M) {
            if (Setting.values[setting_tcm] && group->group_name <= group_C) {
                slider_width = cal_slider_width_by_level(slider_width_max,
                                                         int8_2_uint8(slider_struct->group->flash_level_M_tcm,
                                                                      MAX_FLASH_LEVEL_M_TCM),
                                                         MAX_FLASH_LEVEL_M_TCM);
            } else {
                slider_width = cal_slider_width_by_level(slider_width_max,
                                                         slider_struct->group->flash_level_M,
                                                         get_m_level_max());
            }
        }
    } else if (Param.ctrl_type == type_lamp) {
        if (slider_struct->group->stylish_lamp_mode == lamp_off) {
            slider_width = 0;
        }
#if LAMP_PROP
            else if (slider_struct->group->stylish_lamp_mode == lamp_prop) {
                slider_width = slider_width_max;
            }
#endif
        else {
            slider_width = cal_slider_width_by_level(slider_width_max,
                                                     slider_struct->group->stylish_lamp_level,
                                                     MAX_LAMP_LEVEL);
        }
    }
    // 设置大小
    lv_obj_set_size(slider, slider_width, LV_PCT(100));
    lv_obj_set_style_bg_color(slider, lv_color_hex(Main_Color), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(slider, LV_OPA_COVER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_align(slider, LV_ALIGN_LEFT_MID, 0, 0);
    // 设置直角
    lv_obj_set_style_radius(slider, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(slider, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    // lv_obj_clear_flag(slider, LV_OBJ_FLAG_CLICKABLE);
    // 事件冒泡到父级
    lv_obj_add_flag(slider, LV_OBJ_FLAG_EVENT_BUBBLE);
    lv_obj_clear_flag(slider, LV_OBJ_FLAG_SCROLLABLE);

    // 模式文字水平布局
    lv_obj_t *label_layout = lv_obj_create(slider_bg);
    lv_obj_add_style(label_layout, &PageManager.default_style, 0);
    lv_obj_set_style_bg_opa(label_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_size(label_layout, LV_PCT(100), LV_PCT(100));
    lv_obj_set_flex_flow(label_layout, LV_FLEX_FLOW_ROW);
    lv_obj_align(label_layout, LV_ALIGN_LEFT_MID, 0, 0);
    lv_obj_set_style_pad_gap(label_layout, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_move_foreground(label_layout);
    lv_obj_set_flex_align(label_layout, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    // lv_obj_set_style_pad_left(label_layout, 22, 0);
    lv_obj_add_flag(label_layout, LV_OBJ_FLAG_EVENT_BUBBLE);

    // 区分能级调节与造型灯调节
    lv_obj_t *type_obj = lv_obj_create(label_layout);
    lv_obj_add_style(type_obj, &PageManager.default_style, 0);
    lv_obj_set_style_pad_right(type_obj, 10, 0);
    lv_obj_set_size(type_obj, slider_comp_height + 10, LV_PCT(100));
    lv_obj_set_style_bg_opa(type_obj, LV_OPA_TRANSP, 0);
    lv_obj_clear_flag(type_obj, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_add_flag(type_obj, LV_OBJ_FLAG_EVENT_BUBBLE);
    lv_obj_add_event_cb(type_obj, type_obj_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    lv_obj_add_event_cb(type_obj, free_struct_cb, LV_EVENT_DELETE, NULL);
    TypeObjStruct *to_struct = malloc(sizeof(TypeObjStruct));
    if (to_struct == NULL) {
        // 如果分配失败，处理错误
        LV_LOG("to_struct Memory allocation failed\n");
        return (SliderObjStruct) {0};
    }

    lv_coord_t font_height = lv_font_get_line_height(FONT_SLIDER_LEVEL);
    lv_obj_t *mode_label = lv_label_custom_create(type_obj, mode_str, FONT_SLIDER_LEVEL, lv_color_white(),
                                                  LV_TEXT_ALIGN_CENTER);
    // TCM组件
    lv_obj_t *tcm_obj = comp_tcm_init(slider_bg);
    lv_obj_add_flag(tcm_obj, LV_OBJ_FLAG_HIDDEN);
    SliderOption.tcm_obj = tcm_obj;

    if (Param.ctrl_type == type_flash) {
        if (group->mode == mode_TTL) {
            lv_obj_set_style_text_color(mode_label, lv_color_hex(TTL_Color), 0);
        } else if (group->mode == mode_M) {
            lv_obj_set_style_text_color(mode_label, lv_color_white(), 0);
            // 调整视觉效果
            // lv_obj_set_style_pad_right(type_obj, 10, 0);
        } else if (group->mode == mode_OFF) {
            lv_obj_set_style_text_color(mode_label, lv_color_hex(OFF_Color), 0);
        }
        update_tcm_obj(tcm_obj, group);
    }
    // mode_label垂直居中
    lv_obj_set_size(mode_label, LV_PCT(100), font_height);
    lv_obj_center(mode_label);

    // 造型灯图标
    lv_obj_t *lamp_img = lv_img_custom_create(type_obj, &icon_lamp);
    lv_obj_center(lamp_img);

    // 切换模式label和造型灯img
    switch_type_obj(mode_label, lamp_img);

    // 将模式obj和造型灯obj存入type_obj的user_data
    to_struct->mode_label = mode_label;
    to_struct->lamp_img = lamp_img;
    lv_obj_set_user_data(type_obj, to_struct);

    lv_coord_t level_text_width = lv_txt_get_width("1/256", strlen("1/256"), FONT_SLIDER_LEVEL, 0,
                                                   LV_TEXT_FLAG_EXPAND);
    lv_coord_t sub_level_text_width = lv_txt_get_width("+0.9", strlen("+0.9"), FONT_SLIDER_SUB_LEVEL, 0,
                                                       LV_TEXT_FLAG_EXPAND);

    lv_obj_t *level_label = lv_label_custom_create(label_layout, level_str, FONT_SLIDER_LEVEL, lv_color_white(),
                                                   LV_TEXT_ALIGN_LEFT);
    lv_obj_set_size(level_label, level_text_width, font_height);
    if (Param.ctrl_type == type_flash) {
        if (group->mode == mode_TTL) {
            lv_obj_set_style_text_color(level_label, lv_color_hex(TTL_Color), 0);
            // free_and_clear(level_str);
        } else if (group->mode == mode_OFF) {
            lv_obj_set_style_text_color(level_label, lv_color_hex(OFF_Color), 0);
        }
    } else if (Param.ctrl_type == type_lamp) {
        if (group->stylish_lamp_mode == lamp_off) {
            lv_obj_set_style_text_color(level_label, lv_color_hex(OFF_Color), 0);
        }
    }

    lv_obj_t *span_group = lv_spangroup_create(label_layout);
    lv_obj_set_size(span_group, level_text_width + sub_level_text_width, font_height);
    lv_spangroup_set_align(span_group, LV_TEXT_ALIGN_CENTER);
    lv_obj_clear_flag(span_group, LV_OBJ_FLAG_CLICKABLE);

    lv_span_t *span_group_level = lv_spangroup_new_span(span_group);
    lv_span_set_text(span_group_level, level_str);
    lv_style_set_text_color(&span_group_level->style, lv_color_white());
    lv_style_set_text_font(&span_group_level->style, FONT_SLIDER_LEVEL);
    lv_span_t *span_group_sub_level = lv_spangroup_new_span(span_group);
    lv_span_set_text(span_group_sub_level, sub_level_str);
    lv_style_set_text_color(&span_group_sub_level->style, lv_color_white());
    lv_style_set_text_font(&span_group_sub_level->style, FONT_SLIDER_SUB_LEVEL);
    // free_and_clear(sub_level_str);

    switch_label_obj(sub_level_str, span_group, level_label, group);

    lv_obj_t *mask = lv_obj_create(slider_comp);
    // 忽略父对象的layout布局
    lv_obj_add_flag(mask, LV_OBJ_FLAG_IGNORE_LAYOUT);
    // lv_obj_set_size(mask, lv_obj_get_width(slider_comp), lv_obj_get_height(slider_comp));
    lv_obj_set_size(mask, lv_obj_get_style_width(slider_comp, 0), lv_obj_get_style_height(slider_comp, 0));
    lv_obj_add_style(mask, &PageManager.default_style, 0);
    lv_obj_set_style_bg_color(mask, lv_color_black(), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(mask, LV_OPA_50, 0);
    lv_obj_clear_flag(mask, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_add_flag(mask, LV_OBJ_FLAG_HIDDEN);
    if ((Param.ctrl_type == type_flash && group->mode == mode_OFF) ||
        (Param.ctrl_type == type_lamp && group->stylish_lamp_mode == lamp_off)) {
        lv_obj_clear_flag(mask, LV_OBJ_FLAG_HIDDEN);
    }

    // 聚焦边框创建
    border_focused_obj_init(slider_comp, &Pages.page[Page_Home], Focused_Scroll_Parent | Focused_Editing_Mode);

    SliderOption.span_group = span_group;
    SliderOption.span_group_level = span_group_level;
    SliderOption.span_group_sub_level = span_group_sub_level;
    SliderOption.slider_bg = slider_bg;
    SliderOption.mode_label = mode_label;
    SliderOption.type_obj = type_obj;
    SliderOption.level_label = level_label;
    SliderOption.mask = mask;

    return SliderOption;
}

LevelTypeEnum get_level_type(const void *img_src) {
    LevelTypeEnum level_type;
    if (img_src == &icon_lamp) {
        level_type = level_lamp;
    } else if (img_src == &icon_zoom) {
        level_type = level_zoom;
    } else {
        level_type = level_flash;
    }
    return level_type;
}

SliderObjStruct comp_option_slider_init(lv_obj_t *parent, const void *img_src, GroupStruct *group) {
    // GroupStruct *group = get_grp_struct(grp_name);
    uint8_t level_max;
    uint8_t *level;
    // char *level_str = malloc(6 * sizeof(char));
    char level_str[20], sub_level_str[6];
    const LevelTypeEnum level_type = get_level_type(img_src);
    if (level_type == level_lamp) {
        if (group->stylish_lamp_mode == lamp_off) {
            strncpy(level_str, get_text(text_off), sizeof(level_str));
        }
#if LAMP_PROP
            else if (group->stylish_lamp_mode == lamp_prop) {
                strncpy(level_str, "PROP", sizeof(level_str));
            }
#endif
        else if (group->stylish_lamp_mode == lamp_manual) {
            level = &group->stylish_lamp_level;
            sprintf(level_str, "%d0%%", *level);
        }
        level_max = MAX_LAMP_LEVEL;
        // sprintf(level_str, "%d%%", *level);
    } else if (level_type == level_zoom) {
        if (group->is_auto_zoom) {
            strncpy(level_str, get_text(text_auto), sizeof(level_str));
        } else {
            level = &group->zoom_level;
            strncpy(level_str, zoom_level_text[*level], sizeof(level_str));
#if ProductModel == QZ_F
            if (Setting.values[setting_zoom_disp] == zoom_aps) {
                strncpy(level_str, zoom_aps_level_text[*level], sizeof(level_str));
            }
#endif
        }
        level_max = MAX_ZOOM_LEVEL;
        // level_str = zoom_level_text[*level];
    } else {
        level = get_flash_level(group);
        level_max = get_flash_level_max(group);
        if (group->mode == mode_M) {
            // level_str = get_str_flash_level_M(*level);
            if (Setting.values[setting_tcm] && group->group_name <= group_C) {
                if (group->flash_level_M_tcm > 0) {
                    snprintf(level_str, sizeof(level_str), "+%d.%d", group->flash_level_M_tcm / 10,
                             group->flash_level_M_tcm % 10);
                } else if (group->flash_level_M_tcm < 0) {
                    snprintf(level_str, sizeof(level_str), "-%d.%d", -group->flash_level_M_tcm / 10,
                             -group->flash_level_M_tcm % 10);
                } else {
                    snprintf(level_str, sizeof(level_str), "0.0");
                }
            } else {
                // Setting.roller_values[roller_min_power]前三项为分数
                if (Setting.roller_values[roller_min_power] / MAX_FLASH_LEVEL_INDEX == 0) {
                    strncpy(level_str, get_str_flash_level_M(group->flash_level_M), sizeof(level_str));
                    get_str_flash_sub_level_M(group->flash_level_M, sub_level_str, sizeof(sub_level_str));
                } else {
                    strncpy(level_str, get_decimals_str_flash_level_M(group->flash_level_M), sizeof(level_str));
                    sub_level_str[0] = '\0';
                }
            }
        } else if (group->mode == mode_TTL) {
            // level_str = get_str_flash_level_TTL(*level);
            strncpy(level_str, get_str_flash_level_TTL(*level), sizeof(level_str));
            sub_level_str[0] = '\0';
        } else {
            // level_str = "-----";
            strncpy(level_str, "-----", sizeof(level_str));
            sub_level_str[0] = '\0';
        }
    }
    // 确保以 '\0' 结尾
    level_str[sizeof(level_str) - 1] = '\0';
    // free_and_clear(level_str);

    lv_obj_t *h_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW);
    lv_obj_set_size(h_layout, LV_PCT(100), slider_comp_option_height);
    lv_obj_set_style_bg_opa(h_layout, LV_OPA_COVER, 0);
    lv_obj_set_style_bg_color(h_layout, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_set_style_radius(h_layout, RADIUS_DEFAULT, 0);
    lv_obj_set_style_pad_gap(h_layout, 0, 0);
    lv_obj_set_style_clip_corner(h_layout, true, 0);
    // 传入组别信息便于编码器滚动tile
    lv_obj_set_user_data(h_layout, group);

    lv_obj_t *img_layout = lv_layout_custom_create(h_layout, LV_FLEX_FLOW_ROW);
    const uint8_t slider_comp_option_width = slider_comp_option_height - 8;
    lv_obj_set_size(img_layout, slider_comp_option_width, slider_comp_option_height);
    lv_obj_set_flex_align(img_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_bg_opa(img_layout, LV_OPA_COVER, 0);
    lv_obj_set_style_bg_color(img_layout, lv_color_hex(Bg_Color_Gray), 0);
    lv_obj_t *img = lv_img_custom_create(img_layout, img_src);

    lv_obj_t *slider_bg = lv_obj_create(h_layout);
    lv_obj_set_style_bg_opa(slider_bg, LV_OPA_TRANSP, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(slider_bg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_coord_t slider_width_max = Screen_Width - slider_layout_padding * 2 - slider_comp_option_width;
    lv_obj_set_size(slider_bg, slider_width_max, LV_PCT(100));
    lv_obj_clear_flag(slider_bg, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(slider_bg, LV_SCROLLBAR_MODE_OFF);
    lv_obj_add_style(slider_bg, &PageManager.default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_add_event_cb(slider_bg, slider_event_handler, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(slider_bg, free_struct_cb, LV_EVENT_DELETE, NULL);

    // 滑动条结构体用户数据
    SliderStruct *slider_struct = (SliderStruct *) malloc(sizeof(SliderStruct));
    if (slider_struct == NULL) {
        // 如果分配失败，处理错误
        LV_LOG("comp_option_slider Memory allocation failed\n");
        return (SliderObjStruct) {0};
    }
    slider_struct->level_max = level_max;
    slider_struct->slider_type = slider_option;
    slider_struct->level_type = level_type;
    slider_struct->group = group;
    // slider_struct->placeholders = 255;
    lv_obj_set_user_data(slider_bg, slider_struct);

    lv_obj_t *slider = lv_obj_create(slider_bg);
    lv_obj_set_style_border_width(slider, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_coord_t slider_width = 0;
    if (level_type == level_flash) {
        if (group->mode == mode_TTL) {
            slider_width = cal_slider_width_by_level(slider_width_max,
                                                     slider_struct->group->flash_level_TTL,
                                                     MAX_FLASH_LEVEL_TTL);
        } else if (group->mode == mode_M) {
            if (Setting.values[setting_tcm] && group->group_name <= group_C) {
                slider_width = cal_slider_width_by_level(slider_width_max,
                                                         int8_2_uint8(slider_struct->group->flash_level_M_tcm,
                                                                      MAX_FLASH_LEVEL_M_TCM),
                                                         MAX_FLASH_LEVEL_M_TCM);
            } else {
                slider_width = cal_slider_width_by_level(slider_width_max,
                                                         slider_struct->group->flash_level_M,
                                                         get_m_level_max());
            }
        }
    } else if (level_type == level_lamp) {
        if (slider_struct->group->stylish_lamp_mode == lamp_off) {
            slider_width = 0;
        }
#if LAMP_PROP
            else if (slider_struct->group->stylish_lamp_mode == lamp_prop) {
                slider_width = slider_width_max;
            }
#endif
        else {
            slider_width = cal_slider_width_by_level(slider_width_max,
                                                     slider_struct->group->stylish_lamp_level,
                                                     MAX_LAMP_LEVEL);
        }

    } else if (level_type == level_zoom) {
        if (slider_struct->group->is_auto_zoom) {
            slider_width = slider_width_max;
        } else {
            slider_width = cal_slider_width_by_level(slider_width_max,
                                                     slider_struct->group->zoom_level,
                                                     MAX_ZOOM_LEVEL);
        }
    }

    // 设置大小
    lv_obj_set_size(slider, slider_width, LV_PCT(100));
    lv_obj_set_style_bg_color(slider, lv_color_hex(Main_Color), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(slider, LV_OPA_COVER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_align(slider, LV_ALIGN_LEFT_MID, 0, 0);
    // 设置直角
    lv_obj_set_style_radius(slider, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(slider, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    // lv_obj_clear_flag(slider, LV_OBJ_FLAG_CLICKABLE);
    // 事件冒泡到父级
    lv_obj_add_flag(slider, LV_OBJ_FLAG_EVENT_BUBBLE);
    lv_obj_clear_flag(slider, LV_OBJ_FLAG_SCROLLABLE);

    lv_obj_t *label_layout = lv_layout_custom_create(slider_bg, LV_FLEX_FLOW_ROW);
    lv_obj_set_size(label_layout, LV_PCT(100), slider_comp_option_height);
    lv_obj_move_foreground(label_layout);
    lv_obj_set_flex_align(label_layout, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_pad_left(label_layout, 70, 0);
    lv_obj_set_style_bg_opa(label_layout, LV_OPA_TRANSP, 0);
    lv_obj_clear_flag(label_layout, LV_OBJ_FLAG_CLICKABLE);

    // 字体高度
    lv_coord_t font_height = lv_font_get_line_height(FONT_SLIDER_LEVEL);
    // 主能级宽度
    lv_coord_t level_text_width = lv_txt_get_width("1/256", strlen("1/256"), FONT_SLIDER_LEVEL, 0,
                                                   LV_TEXT_FLAG_EXPAND);
    lv_coord_t zoom_text_width = lv_txt_get_width("135mm", strlen("135mm"), FONT_SLIDER_LEVEL, 0,
                                                  LV_TEXT_FLAG_EXPAND);
    // 次能级宽度
    lv_coord_t sub_level_text_width = lv_txt_get_width("+0.9", strlen("+0.9"), FONT_SLIDER_SUB_LEVEL, 0,
                                                       LV_TEXT_FLAG_EXPAND);
    SliderObjStruct SliderOption;
    lv_obj_t *level_label = lv_label_custom_create(label_layout, level_str, FONT_SLIDER_LEVEL, lv_color_white(),
                                                   LV_TEXT_ALIGN_LEFT);
    lv_obj_set_size(level_label, zoom_text_width, font_height);
    if (img_src == &icon_flash && group->mode == mode_TTL) {
        lv_obj_set_style_text_color(level_label, lv_color_hex(TTL_Color), 0);
    } else if (img_src == &icon_lamp && group->stylish_lamp_mode == lamp_off) {
        lv_obj_set_style_text_color(level_label, lv_color_hex(OFF_Color), 0);
    }
    SliderOption.level_label = level_label;

    if (level_type == level_flash) {
        lv_obj_t *span_group = lv_spangroup_create(label_layout);
        lv_obj_set_size(span_group, level_text_width + sub_level_text_width, font_height);
        lv_spangroup_set_align(span_group, LV_TEXT_ALIGN_LEFT);
        lv_obj_clear_flag(span_group, LV_OBJ_FLAG_CLICKABLE);
        // lv_obj_set_style_border_width(span_group, 3, 0);
        lv_span_t *span_group_level, *span_group_sub_level;
        span_group_level = lv_spangroup_new_span(span_group);
        lv_span_set_text(span_group_level, level_str);
        lv_style_set_text_color(&span_group_level->style, lv_color_hex(0xFFFFFF));
        lv_style_set_text_font(&span_group_level->style, FONT_SLIDER_LEVEL);
        span_group_sub_level = lv_spangroup_new_span(span_group);
        lv_span_set_text(span_group_sub_level, sub_level_str);
        lv_style_set_text_color(&span_group_sub_level->style, lv_color_hex(0xFFFFFF));
        lv_style_set_text_font(&span_group_sub_level->style, FONT_SLIDER_SUB_LEVEL);
        // lv_style_set_text_opa(&span_group_sub_level->style, LV_OPA_TRANSP);

        SliderOption.span_group = span_group;
        SliderOption.span_group_level = span_group_level;
        SliderOption.span_group_sub_level = span_group_sub_level;
        // 切换显示文字的obj
        switch_label_obj(sub_level_str, span_group, level_label, group);

        // TCM组件
        lv_obj_t *tcm_obj = comp_tcm_init(slider_bg);
        lv_obj_add_flag(tcm_obj, LV_OBJ_FLAG_HIDDEN);
        SliderOption.tcm_obj = tcm_obj;
        update_tcm_obj(tcm_obj, group);
    }
    SliderOption.slider_bg = slider_bg;

    border_focused_obj_init(h_layout, &Pages.page[Page_Group_Info], Focused_Scroll_Tile | Focused_Editing_Mode);

    return SliderOption;
}

static void more_grp_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    // LV_LOG("more_grp_cb code %d\n", code);
    if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_ESC) {
                // 创建页面
                pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page, LV_ALIGN_OUT_LEFT_MID,
                              lv_color_black());
                page_click_anim(pos_left, &Pages.page[Page_Multi], anim_slide);
                return;
            } else if (c == LV_KEY_END) {
                // 创建页面
                pm_click_to_page_control_center();
            }
            if (c != LV_KEY_ENTER) {
                return;
            }
        }
        if (Anim.is_finished_anim) {
            pm_creat_page(PageManager.main_page->pos[pos_next].page, PageManager.main_page, LV_ALIGN_OUT_RIGHT_TOP,
                          lv_color_black());
            lv_obj_set_parent(status_bar, PageManager.main_page->obj);
            page_click_anim(pos_right, &Pages.page[Page_Group_Switch], anim_slide);
        }
    }
}

static void more_label_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_VALUE_CHANGED) {
        lv_obj_t *tar_obj = lv_event_get_target(e);
        lv_label_set_text(tar_obj, get_text(text_more));
    }
}

void slider_layout_home(lv_obj_t *parent) {
    // 创建垂直布局obj
    lv_obj_t *v_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_size(v_layout, LV_PCT(100), slider_comp_height * 3 + 10 * 2);
    lv_obj_set_style_bg_opa(v_layout, LV_OPA_TRANSP, 0);
    // // 设置为垂直布局
    // lv_obj_set_flex_flow(v_layout, LV_FLEX_FLOW_COLUMN);
    // 将垂直布局容器居中
    // lv_obj_center(v_layout);
    // lv_obj_set_align(v_layout, LV_ALIGN_TOP_MID);
    // lv_obj_align(v_layout, LV_ALIGN_TOP_MID, 0, 56);
    // lv_obj_set_style_pad_hor(v_layout, slider_layout_padding, 0);
    lv_obj_set_scrollbar_mode(v_layout, LV_SCROLLBAR_MODE_OFF);
    // 设置布局属性
    // 设置子对象间的间距
    lv_obj_set_style_pad_gap(v_layout, 10, 0);
    // 将子对象居中
    // lv_obj_set_flex_align(v_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    // 设置滚动方向，便于焦点判断
    lv_obj_set_scroll_dir(v_layout, LV_DIR_VER);

    for (int i = 0; i < GroupItemCount; ++i) {
        Param.group[i].slider_home = comp_group_slider_init(v_layout, &Param.group[i]);
    }

    lv_obj_t *more_grp = lv_layout_custom_create(v_layout, LV_FLEX_FLOW_ROW);
    lv_obj_set_size(more_grp, LV_PCT(100), slider_comp_height);
    lv_obj_set_style_bg_color(more_grp, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_set_style_radius(more_grp, RADIUS_DEFAULT, 0);
    lv_obj_set_style_pad_gap(more_grp, 4, 0);
    lv_obj_set_flex_align(more_grp, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_event_cb(more_grp, more_grp_cb, LV_EVENT_ALL, NULL);
    // 聚焦边框创建
    border_focused_obj_init(more_grp, &Pages.page[Page_Home], Focused_Scroll_Parent | Focused_Opr_Parent);

    lv_obj_t *more_grp_ico = lv_img_custom_create(more_grp, &icon_more_groups);
    // lv_obj_set_style_border_width(more_grp_ico,1,0);
    lv_obj_t *label = lv_label_custom_create(more_grp, get_text(text_more), FONT_SLIDER_LEVEL, lv_color_white(),
                                             LV_TEXT_ALIGN_CENTER);
    lv_obj_add_event_cb(label, more_label_cb, LV_EVENT_VALUE_CHANGED, NULL);
    Param.more_label_obj = label;
    // lv_obj_set_style_border_width(label,1,0);
}

void slider_grp_page_utils_func(lv_obj_t *parent, GroupStruct *grp_name, SliderObjStruct *slider_flash_level,
                                SliderObjStruct *slider_lamp_level, SliderObjStruct *slider_zoom_level) {
    *slider_flash_level = comp_option_slider_init(parent, &icon_flash, grp_name);
    *slider_lamp_level = comp_option_slider_init(parent, &icon_lamp, grp_name);
    *slider_zoom_level = comp_option_slider_init(parent, &icon_zoom, grp_name);
}

lv_obj_t *slider_layout_grp_page(lv_obj_t *page, GroupStruct *grp) {
    // 创建垂直布局obj
    lv_obj_t *v_layout = lv_layout_custom_create(page, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_size(v_layout, LV_PCT(100), opt_slider_layout_height);
    lv_obj_set_style_bg_opa(v_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_style_pad_gap(v_layout, slider_layout_option_padding, 0);
    // // 设置为垂直布局
    // lv_obj_set_flex_flow(v_layout, LV_FLEX_FLOW_COLUMN);
    // 将垂直布局容器居中
    // lv_obj_center(v_layout);
    // lv_obj_set_align(v_layout, LV_ALIGN_TOP_MID);
    lv_obj_align(v_layout, LV_ALIGN_TOP_MID, 0, 12);
    // lv_obj_set_style_pad_hor(v_layout, slider_layout_padding, 0);
    lv_obj_set_scrollbar_mode(v_layout, LV_SCROLLBAR_MODE_OFF);

    // 设置布局属性
    // 设置子对象间的间距
    // lv_obj_set_style_pad_gap(v_layout, 20, 0);
    // 将子对象居中
    // lv_obj_set_flex_align(v_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    slider_grp_page_utils_func(v_layout, grp, &grp->slider_option_flash_level,
                               &grp->slider_option_lamp_level, &grp->slider_option_zoom_level);
    grp->slider_grp_layout = v_layout;
    if (grp->mode == mode_OFF) {
        lv_obj_add_flag(v_layout, LV_OBJ_FLAG_HIDDEN);
    }
    return v_layout;
}
