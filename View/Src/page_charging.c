//
// Created by <PERSON><PERSON> on 2025/3/22.
//

#include "page_charging.h"
#include "page_manager.h"
#include "user.h"
#include "view_model.h"

ChargingPage_t ChargingPage;

#if !IS_REAL_DEVICE
static void charging_key_event_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_KEY) {
        uint32_t key = lv_event_get_key(e);
        if (key == LV_KEY_LEFT) {
            if (Param.electricity_level > 0) {
                ChargingPage.target_electricity_level = Param.electricity_level - 1;
                ChargingPage.need_change_level = 1;
            }
        } else if (key == LV_KEY_RIGHT) {
            if (Param.electricity_level < 2) {
                ChargingPage.target_electricity_level = Param.electricity_level + 1;
                ChargingPage.need_change_level = 1;
            }
        }
    }
}
#endif

static void charging_animation_cb(lv_timer_t *timer) {
    uint8_t start_percentage = 0;
    uint8_t end_percentage = 100;

    // 根据电量级别调整动画范围
    if (Param.electricity_level == 1) {
        start_percentage = 33;
        end_percentage = 100;
    } else if (Param.electricity_level == 2 || Param.electricity_level == 3) {
        start_percentage = 66;
        end_percentage = 100;
    }

    // 计算当前百分比
    ChargingPage.charging_percentage =
            (ChargingPage.charging_percentage + 1) % (end_percentage - start_percentage + 1);
    uint8_t current_percentage = start_percentage + ChargingPage.charging_percentage;

    // 设置电池填充高度
    lv_obj_set_height(ChargingPage.battery_fill_obj, (170 * current_percentage) / 100);
    lv_obj_align(ChargingPage.battery_fill_obj, LV_ALIGN_BOTTOM_MID, 0, 0);

    // 检查是否需要切换电量级别
    if (ChargingPage.need_change_level && ChargingPage.charging_percentage == 0) {
        // 更新电量级别
        Param.electricity_level = ChargingPage.target_electricity_level;
        ChargingPage.need_change_level = 0;

        // 根据新的电量级别设置定时器时间,使视觉效果更好
        if (Param.electricity_level == 0) {
            lv_timer_set_period(ChargingPage.charging_timer, 30);
        } else if (Param.electricity_level == 1) {
            lv_timer_set_period(ChargingPage.charging_timer, 33);
        } else if (Param.electricity_level == 2 || Param.electricity_level == 3) {
            lv_timer_set_period(ChargingPage.charging_timer, 36);
        }

        // 保持当前高度，避免闪烁
        lv_obj_set_height(ChargingPage.battery_fill_obj, (170 * end_percentage) / 100);
    }

    // 检查是否达到满电状态
    if (Param.battery_status == battery_complete && current_percentage >= 100) {
        // 停止充电动画
        lv_timer_del(ChargingPage.charging_timer);
        // 隐藏充电图标
        lv_obj_t *battery_img = lv_obj_get_child(ChargingPage.battery_obj, 1);
        if (battery_img) {
            lv_obj_add_flag(battery_img, LV_OBJ_FLAG_HIDDEN);
        }
    }
}

static void battery_low_blink_cb(lv_timer_t *timer) {
    if (Param.battery_status >= battery_low) {
        // 闪烁3秒，每秒2次(亮灭各算一次) 6
        // 闪烁5秒，每秒2次(亮灭各算一次) 10
        if (ChargingPage.blink_count < 8) {
            ChargingPage.is_visible = !ChargingPage.is_visible;
            if (ChargingPage.is_visible) {
                lv_obj_set_style_border_color(ChargingPage.battery_obj, lv_color_hex(BAT_LOW_Color), LV_PART_MAIN);
                lv_obj_set_style_bg_color(ChargingPage.battery_head_obj, lv_color_hex(BAT_LOW_Color), LV_PART_MAIN);
            } else {
                lv_obj_set_style_border_color(ChargingPage.battery_obj, lv_color_black(), LV_PART_MAIN);
                lv_obj_set_style_bg_color(ChargingPage.battery_head_obj, lv_color_black(), LV_PART_MAIN);
            }
            ChargingPage.blink_count++;
        } else {
            // 闪烁结束后隐藏所有对象
            lv_obj_add_flag(ChargingPage.battery_obj, LV_OBJ_FLAG_HIDDEN);
            lv_obj_add_flag(ChargingPage.battery_head_obj, LV_OBJ_FLAG_HIDDEN);
            lv_timer_del(ChargingPage.blink_timer);
            // 关机
            set_device_off();
        }
    }
}

lv_obj_t *page_bg_init() {
    lv_obj_t *bg_obj = lv_obj_create(lv_scr_act());
    lv_obj_set_style_radius(bg_obj, 0, 0);
    lv_obj_set_style_border_width(bg_obj, 0, 0);
    lv_obj_set_size(bg_obj, Screen_Width, Screen_Height);
    lv_obj_clear_flag(bg_obj, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(bg_obj, LV_SCROLLBAR_MODE_OFF);
    lv_obj_set_style_bg_color(bg_obj, lv_color_black(), 0);
    lv_obj_set_style_pad_all(bg_obj, 0, 0);

    return bg_obj;
}

void page_charging_init() {
    lv_obj_t *page = page_bg_init();
#if !IS_REAL_DEVICE
    Param.battery_status = battery_complete;
    // 添加键盘事件监听
    lv_obj_add_event_cb(page, charging_key_event_cb, LV_EVENT_ALL, NULL);
    lv_group_t *g = lv_group_create();
    set_indev_group(g);
    lv_group_add_obj(g, page);
#endif
    // 创建电池外框
    ChargingPage.battery_obj = lv_obj_create(page);
    lv_obj_set_size(ChargingPage.battery_obj, 128, 218);
    lv_obj_set_style_radius(ChargingPage.battery_obj, 22, LV_PART_MAIN);
    // 根据电池状态设置颜色
    if (Param.battery_status >= battery_low) {
        lv_obj_set_style_border_color(ChargingPage.battery_obj, lv_color_hex(BAT_LOW_Color), LV_PART_MAIN);
    } else {
        lv_obj_set_style_border_color(ChargingPage.battery_obj, lv_color_white(), LV_PART_MAIN);
    }
    lv_obj_set_style_border_width(ChargingPage.battery_obj, 14, LV_PART_MAIN);
    lv_obj_set_style_bg_color(ChargingPage.battery_obj, lv_color_black(), LV_PART_MAIN);
    lv_obj_align(ChargingPage.battery_obj, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ChargingPage.battery_obj, 10, 0);

    // 创建电池填充部分（绿色电量）
    ChargingPage.battery_fill_obj = lv_obj_create(ChargingPage.battery_obj);
    // w 128 - 14 * 2 - 10 * 2 = 80 （外框宽-border*2-内边距*2）
    // h 170
    if (Param.battery_status == battery_complete) {
        lv_obj_set_size(ChargingPage.battery_fill_obj, 80, 170);
    } else {
        lv_obj_set_size(ChargingPage.battery_fill_obj, 80, 0);
    }
    lv_obj_set_style_radius(ChargingPage.battery_fill_obj, 4, LV_PART_MAIN);
    lv_obj_set_style_bg_color(ChargingPage.battery_fill_obj, lv_color_hex(Charge_Color), LV_PART_MAIN);
    lv_obj_set_style_border_width(ChargingPage.battery_fill_obj, 0, LV_PART_MAIN);
    lv_obj_align(ChargingPage.battery_fill_obj, LV_ALIGN_BOTTOM_MID, 0, 0);
    lv_obj_clear_flag(ChargingPage.battery_fill_obj, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(ChargingPage.battery_fill_obj, LV_SCROLLBAR_MODE_OFF);

    // 根据电池状态控制填充部分的显示
    if (Param.battery_status >= battery_low) {
        lv_obj_add_flag(ChargingPage.battery_fill_obj, LV_OBJ_FLAG_HIDDEN);
    }

    // 创建电池顶部的凸起部分
    ChargingPage.battery_head_obj = lv_obj_create(page);
    lv_obj_set_size(ChargingPage.battery_head_obj, 42, 42);
    lv_obj_set_style_radius(ChargingPage.battery_head_obj, 22, LV_PART_MAIN);
    // 根据电池状态设置颜色
    if (Param.battery_status >= battery_low) {
        lv_obj_set_style_bg_color(ChargingPage.battery_head_obj, lv_color_hex(BAT_LOW_Color), LV_PART_MAIN);
    } else {
        lv_obj_set_style_bg_color(ChargingPage.battery_head_obj, lv_color_white(), LV_PART_MAIN);
    }
    lv_obj_set_style_border_width(ChargingPage.battery_head_obj, 0, LV_PART_MAIN);
    lv_obj_align_to(ChargingPage.battery_head_obj, ChargingPage.battery_obj, LV_ALIGN_OUT_TOP_MID, 0, 30);
    lv_obj_move_foreground(ChargingPage.battery_obj);
    lv_obj_move_foreground(ChargingPage.battery_fill_obj);

    // 创建电池图标
    lv_obj_t *battery_img = lv_img_custom_create(ChargingPage.battery_obj, &icon_charging);
    lv_obj_center(battery_img);
    // 只在充电状态时显示图标
    if (Param.battery_status != battery_charging) {
        lv_obj_add_flag(battery_img, LV_OBJ_FLAG_HIDDEN);
    }

    if (Param.battery_status == battery_charging) {
        // 创建充电动画定时器
        // 初始时间为30ms
        ChargingPage.charging_timer = lv_timer_create(charging_animation_cb, 30, NULL);
    } else if (Param.battery_status >= battery_low) {
        ChargingPage.is_low_anim = true;
        // 如果是低电量状态，创建闪烁定时器
        // 每500ms闪烁一次
        ChargingPage.blink_timer = lv_timer_create(battery_low_blink_cb, 500, NULL);
    }

    //Param.charging_page = page;
}