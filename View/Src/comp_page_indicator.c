//
// Created by <PERSON><PERSON> on 2024/7/27.
//

#include "comp_page_indicator.h"
#include "page_manager.h"

// 页面指示器组件
void comp_page_indicator(lv_obj_t *parent, lv_flex_flow_t flow) {
    lv_obj_t *page_indicator_layout = lv_layout_custom_create(parent, flow);
    lv_obj_set_style_pad_gap(page_indicator_layout, 4, 0);
    lv_obj_set_size(page_indicator_layout, LV_PCT(100), 4);
    lv_obj_set_style_bg_opa(page_indicator_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_flex_align(page_indicator_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_t *indicator_selected = lv_obj_create(page_indicator_layout);
    lv_obj_add_style(indicator_selected, &PageManager.default_style, 0);
    lv_obj_set_size(indicator_selected, 32 - 4 - 6, LV_PCT(100));
    lv_obj_set_style_radius(indicator_selected, 3, 0);
    lv_obj_set_style_bg_color(indicator_selected, lv_color_white(), 0);
    lv_obj_t *indicator_unselected = lv_obj_create(page_indicator_layout);
    lv_obj_add_style(indicator_unselected, &PageManager.default_style, 0);
    lv_obj_set_size(indicator_unselected, 6, LV_PCT(100));
    lv_obj_set_style_radius(indicator_unselected, 3, 0);
    lv_obj_set_style_bg_color(indicator_unselected, lv_color_hex(Indicator_Gray_Color), 0);
}
