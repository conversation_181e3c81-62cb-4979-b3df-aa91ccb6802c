#include "my_slider_test.h"

static lv_style_t style_slider_part_main;
static lv_style_t style_slider_part_indicator;
static lv_style_t style_slider_part_knob;
static lv_style_t style_slider_part_knob_pr;
static lv_style_t style_slider_label;

static void slider_set_brightness_event_handler(lv_event_t *e);

void my_lock_ui(void);
static void my_lock_event_cb(lv_event_t *e);

void my_slider_test(void)
{
    lv_obj_t *cont = lv_obj_create(lv_layer_top());
    lv_obj_remove_style_all(cont);
    lv_obj_set_size(cont, lv_pct(100), lv_pct(100));
    lv_obj_set_style_bg_color(cont, lv_color_hex(0x000000), 0);
    lv_obj_set_style_bg_opa(cont, LV_OPA_COVER, 0);
    lv_obj_set_flex_flow(cont, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(cont, LV_FLEX_ALIGN_SPACE_AROUND, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_set_style_pad_top(cont, 0, 0);
    lv_obj_set_style_pad_left(cont, 20, 0);
    lv_obj_set_style_pad_right(cont, 20, 0);
    lv_obj_set_style_pad_row(cont, 20, 0);
    lv_obj_set_style_clip_corner(cont, true, 0);
    // lv_obj_set_scroll_snap_y(cont, LV_SCROLL_SNAP_START);
    lv_obj_set_scroll_dir(cont, LV_DIR_VER);
    lv_obj_set_scrollbar_mode(cont, LV_SCROLLBAR_MODE_OFF);
    lv_obj_t *cont_sub;
    cont_sub = lv_obj_create(cont);
    lv_obj_remove_style_all(cont_sub);
    lv_obj_set_size(cont_sub, 180, 180);
    // lv_obj_set_style_bg_color(cont_sub, lv_color_hex(0x686868), 0);
    lv_obj_set_style_bg_opa(cont_sub, LV_OPA_TRANSP, 0);
    lv_obj_set_style_radius(cont_sub, 16, 0);
    lv_obj_set_style_pad_row(cont_sub, 8, 0);
    lv_obj_set_flex_flow(cont_sub, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(cont_sub, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);

    lv_obj_t *slider[2];
    for (int i = 0; i < 2; i++)
    {
        slider[i] = my_slider_create(cont_sub); // 自定义的 slider
        lv_slider_set_range(slider[i], 0, 100);
        lv_slider_set_value(slider[i], 0, LV_ANIM_OFF);
        lv_obj_set_size(slider[i], lv_pct(100), lv_pct(40));
        lv_obj_align(slider[i], LV_ALIGN_LEFT_MID, (70 * i) + 30, 0);

        lv_obj_t *label = lv_label_create(slider[i]);
        lv_obj_add_style(label, &style_slider_label, LV_PART_MAIN);
        lv_label_set_text(label, "0%");
        //lv_obj_move_foreground(label);
        lv_obj_align(label, LV_ALIGN_CENTER, 0, 0);
        lv_obj_add_event_cb(slider[i], slider_set_brightness_event_handler, LV_EVENT_VALUE_CHANGED, label);

        //lv_obj_add_style(slider[i], &style_slider_part_main, LV_PART_INDICATOR );
        lv_obj_add_style(slider[i], &style_slider_part_main, LV_PART_MAIN );
        lv_obj_add_style(slider[i], &style_slider_part_indicator, LV_PART_INDICATOR);
        lv_obj_add_style(slider[i], &style_slider_part_knob, LV_PART_KNOB);
        lv_obj_add_style(slider[i], &style_slider_part_knob_pr, LV_PART_KNOB | LV_STATE_PRESSED);
    }

    // 初始化样式
    lv_style_init(&style_slider_part_main);
    lv_style_set_radius(&style_slider_part_main, 16);
    lv_style_set_bg_color(&style_slider_part_main, lv_color_hex(0x686868));
    lv_style_set_bg_opa(&style_slider_part_main, LV_OPA_COVER);

    lv_style_init(&style_slider_part_indicator);
    lv_style_set_radius(&style_slider_part_indicator, 0);
    lv_style_set_bg_color(&style_slider_part_indicator, lv_color_hex(0xffffff));

    lv_style_init(&style_slider_part_knob);
    lv_style_set_pad_all(&style_slider_part_knob, 20);
    lv_style_set_pad_top(&style_slider_part_knob, -50);
    lv_style_set_pad_bottom(&style_slider_part_knob, -50);
    lv_style_set_radius(&style_slider_part_knob, 0);
    lv_style_set_bg_color(&style_slider_part_knob, lv_color_hex(0xffffff));

    lv_style_init(&style_slider_part_knob_pr);
    lv_style_set_transform_width(&style_slider_part_knob_pr, 0);
    lv_style_set_transform_height(&style_slider_part_knob_pr, 0);

    lv_style_init(&style_slider_label);
    lv_style_set_text_color(&style_slider_label, lv_color_hex(0xff0000));
    lv_style_set_text_font(&style_slider_label, &lv_font_montserrat_12);
    //my_lock_ui();
}

static void slider_set_brightness_event_handler(lv_event_t *e)
{
    int32_t slider_value = 0;
    lv_obj_t *slider = lv_event_get_current_target(e);
    lv_obj_t *label = lv_event_get_user_data(e);
    slider_value = lv_slider_get_value(slider);
    LV_LOG_USER("LV_EVENT_VALUE_CHANGED: %d%", slider_value);
    lv_label_set_text_fmt(label, "%d%%", slider_value);
}

void my_lock_ui(void)
{
    lv_obj_t *lock = lv_obj_create(lv_layer_sys());
    lv_obj_set_size(lock, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_bg_color(lock, lv_color_hex(0x000000), 0);
    lv_obj_set_style_bg_opa(lock, LV_OPA_20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_t *lock_label = lv_label_create(lock);
    lv_obj_set_style_text_color(lock_label, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(lock_label, &lv_font_montserrat_12, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_label_set_text(lock_label, "Press 2s to unlock.");
    lv_obj_center(lock_label);
    lv_obj_add_event_cb(lock, my_lock_event_cb, LV_EVENT_LONG_PRESSED, NULL);
}

static void my_lock_event_cb(lv_event_t *e)
{
    //lv_indev_t *indev = lv_indev_active();
    //LV_ASSERT_NULL(indev);
    //lv_indev_set_long_press_time(indev, 2000);
    lv_obj_t *obj = lv_event_get_target(e);      // 获取触发事件的部件(对象)
    lv_event_code_t code = lv_event_get_code(e); // 获取当前部件(对象)触发的事件代码
    LV_LOG_USER("LV_EVENT_LONG_PRESSED\n");
    //lv_obj_delete(obj);
}