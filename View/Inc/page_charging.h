//
// Created by <PERSON><PERSON> on 2025/3/22.
//

#ifndef UI_QZ_N_PAGE_CHARGING_H
#define UI_QZ_N_PAGE_CHARGING_H

#include "lvgl_custom_function.h"

typedef struct {
    // 充电动画定时器
    lv_timer_t *charging_timer;
    // 闪烁动画定时器
    lv_timer_t *blink_timer;
    // 电池填充对象
    lv_obj_t *battery_fill_obj;
    // 电池外框对象
    lv_obj_t *battery_obj;
    // 电池顶部凸起对象
    lv_obj_t *battery_head_obj;
    // 充电百分比
    uint8_t charging_percentage;
    // 目标电量级别
    uint8_t target_electricity_level;
    // 是否需要切换电量级别
    uint8_t need_change_level;
    // 闪烁计数
    uint8_t blink_count;
    // 当前是否可见
    uint8_t is_visible;
    // 是否为低电动画
    bool is_low_anim;
} ChargingPage_t;
extern ChargingPage_t ChargingPage;

void page_charging_init();

#endif //UI_QZ_N_PAGE_CHARGING_H
