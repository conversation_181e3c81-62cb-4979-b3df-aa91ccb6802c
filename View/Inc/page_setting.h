//
// Created by <PERSON><PERSON> on 2024/9/2.
//

#ifndef PAGE_MANAGER_LV_PORT_WIN_CODEBLOCKS_V8_3_PAGE_SETTING_H
#define PAGE_MANAGER_LV_PORT_WIN_CODEBLOCKS_V8_3_PAGE_SETTING_H

#include "lvgl_custom_function.h"
#include "user.h"

extern OptionStruct option_shoot;
extern OptionStruct option_tcm;

void page_setting_init(lv_obj_t *parent);

#endif //PAGE_MANAGER_LV_PORT_WIN_CODEBLOCKS_V8_3_PAGE_SETTING_H
