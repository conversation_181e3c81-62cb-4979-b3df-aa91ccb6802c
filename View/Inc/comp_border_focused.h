//
// Created by <PERSON><PERSON> on 2024/11/15.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_COMP_BORDER_FOCUSED_H
#define LV_PORT_WIN_CODEBLOCKS_V8_COMP_BORDER_FOCUSED_H

#include "user.h"

typedef enum {
    Focused_Default = 0x0000,
    // 父对象要设置滚动方向，便于焦点判断
    // lv_obj_set_scroll_dir(v_layout, LV_DIR_VER);
    Focused_Scroll_Parent = 0x0001,
    Focused_Scroll_Tile = 0x0002,
    Focused_Editing_Mode = 0x0004,
    Focused_Opr_Parent = 0x0008,
    // Focused_EDITED = 0x0008,
    // Focused_HOVERED = 0x0010,
    // Focused_PRESSED = 0x0020,
    // Focused_SCROLLED = 0x0040,
    // Focused_DISABLED = 0x0080,
    //
    // Focused_USER_1 = 0x1000,
    // Focused_USER_2 = 0x2000,
    // Focused_USER_3 = 0x4000,
    // Focused_USER_4 = 0x8000,

    // Focused_Param_ANY = 0xFFFF,
} FocusedParamEnum;

lv_obj_t *border_focused_obj_init(lv_obj_t *parent, PageTypeHandle *page, FocusedParamEnum FocusedParam);

#endif //LV_PORT_WIN_CODEBLOCKS_V8_COMP_BORDER_FOCUSED_H
