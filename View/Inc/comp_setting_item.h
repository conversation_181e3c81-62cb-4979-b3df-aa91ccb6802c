//
// Created by <PERSON><PERSON> on 2024/9/12.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_COMP_SETTING_ITEM_IMG_H
#define LV_PORT_WIN_CODEBLOCKS_V8_COMP_SETTING_ITEM_IMG_H

#include "lvgl_custom_function.h"

typedef enum {
    pop_sync_rf,
    pop_reset
} PopEnum;

void setting_list_layout(lv_obj_t *parent);

void setting_opt_layout(lv_obj_t *parent);

void rotate_img_anim_cb(void *img, int32_t angle);

#endif //LV_PORT_WIN_CODEBLOCKS_V8_COMP_SETTING_ITEM_IMG_H
