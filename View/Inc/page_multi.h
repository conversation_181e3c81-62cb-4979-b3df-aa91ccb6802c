//
// Created by <PERSON><PERSON> on 2024/7/26.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_PAGE_MULTI_H
#define LV_PORT_WIN_CODEBLOCKS_V8_PAGE_MULTI_H

#include "user.h"

typedef enum {
    roller_multi_times,
    roller_multi_hz
} RollerMultiTypeEnumType;

typedef struct {
    RollerMultiTypeEnumType roller_type;
} RollerStructType;

void page_multi_init(lv_obj_t *page);

void comp_fine_tuning_slider_init(lv_obj_t *parent, uint8_t slider_type);

#endif //LV_PORT_WIN_CODEBLOCKS_V8_PAGE_MULTI_H
