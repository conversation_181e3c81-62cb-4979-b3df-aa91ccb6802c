<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="LittlevGL" />
		<Option pch_mode="2" />
		<Option compiler="gcc" />
		<Build>
			<Target title="Debug">
				<Option output="bin/Debug/LittlevGL" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Debug/" />
				<Option type="0" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-g" />
					<Add option="-DLV_CONF_INCLUDE_SIMPLE=1" />
					<Add option="-DWINVER=0x0601" />
					<Add directory="." />
					<Add directory="lvgl" />
					<Add directory="User_Else" />
				</Compiler>
				<Linker>
					<Add option="-mwindows -mconsole" />
					<Add library="mingw32" />
				</Linker>
			</Target>
			<Target title="Release">
				<Option output="bin/Release/LittlevGL" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Release/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-O2" />
				</Compiler>
				<Linker>
					<Add option="-s" />
				</Linker>
			</Target>
		</Build>
		<Compiler>
			<Add option="-Wall" />
			<Add directory="." />
			<Add directory="View/Inc" />
			<Add directory="lvgl" />
			<Add directory="ViewModel/Inc" />
			<Add directory="View/widget" />
		</Compiler>
		<Unit filename="ViewModel/Inc/user.h" />
		<Unit filename="ViewModel/Inc/view_model.h" />
		<Unit filename="ViewModel/Src/user.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="ViewModel/Src/view_model.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Font/font_source_han_sans_bold_32.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Font/font_source_han_sans_bold_40.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Font/font_source_han_sans_bold_50.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Font/font_source_han_sans_medium_18.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Font/font_source_han_sans_regular_22.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Font/font_source_han_sans_regular_30.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Font/font_source_han_sans_regular_40.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Font/font_source_han_sans_regular_50.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/ico_logo.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_add_40.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_battery_low.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_bluetooth.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_brightness.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_camera_comm.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_charging.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_chg_complete.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_decimals.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_disp_type.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_down.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_down_20.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_electricity_level_0.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_electricity_level_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_electricity_level_100.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_electricity_level_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_electricity_level_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_flash.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_fractions.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_front_curtain.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_front_curtain_36.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_grp_color_selected.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_grp_color_unselected.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_high_speed.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_high_speed_36.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_hotshoe.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_lamp.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_lamp_36.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_language.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_loading.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_locked.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_locked_100.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_mid_20.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_more_groups.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_power_disp.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_range_close.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_range_long.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_rear_curtain.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_rear_curtain_36.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_reduce_40.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_reset.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_restore_factory.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_restore_factory_settings.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_rf.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_screen_setting.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_setting.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_single_contact.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_sleep.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_synchronization.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_unlock.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_up.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_up_20.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_vol_off.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_vol_on.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Ico/icon_zoom.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Inc/comp_bar.h" />
		<Unit filename="View/Inc/comp_border_focused.h" />
		<Unit filename="View/Inc/comp_grp_switch.h" />
		<Unit filename="View/Inc/comp_menu_option.h" />
		<Unit filename="View/Inc/comp_page_indicator.h" />
		<Unit filename="View/Inc/comp_setting_item.h" />
		<Unit filename="View/Inc/comp_sleep.h" />
		<Unit filename="View/Inc/comp_slider.h" />
		<Unit filename="View/Inc/comp_status_bar.h" />
		<Unit filename="View/Inc/lvgl_custom_function.h" />
		<Unit filename="View/Inc/multi_language.h" />
		<Unit filename="View/Inc/page_charging.h" />
		<Unit filename="View/Inc/page_control_center.h" />
		<Unit filename="View/Inc/page_group_info.h" />
		<Unit filename="View/Inc/page_grp_switch.h" />
		<Unit filename="View/Inc/page_home.h" />
		<Unit filename="View/Inc/page_manager.h" />
		<Unit filename="View/Inc/page_manager_anim_mode.h" />
		<Unit filename="View/Inc/page_multi.h" />
		<Unit filename="View/Inc/page_setting.h" />
		<Unit filename="View/Inc/page_setting_detail.h" />
		<Unit filename="View/Inc/page_welcome.h" />
		<Unit filename="View/Inc/tinyprintf.h" />
		<Unit filename="View/Inc/utils.h" />
		<Unit filename="View/Src/comp_bar.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/comp_border_focused.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/comp_grp_switch.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/comp_menu_option.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/comp_page_indicator.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/comp_setting_item.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/comp_sleep.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/comp_slider.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/comp_status_bar.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/lvgl_custom_function.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/multi_language.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_charging.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_control_center.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_group_info.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_grp_switch.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_home.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_manager.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_manager_anim_mode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_multi.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_setting.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_setting_detail.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/page_welcome.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/tinyprintf.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/Src/utils.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/widget/my_slider.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/widget/my_slider.h" />
		<Unit filename="View/widget/my_slider_private.h" />
		<Unit filename="View/widget/my_slider_test.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="View/widget/my_slider_test.h" />
		<Unit filename="lv_conf.h" />
		<Unit filename="lv_demos/src/lv_demo.h" />
		<Unit filename="lv_demos/src/lv_demo_benchmark/lv_demo_benchmark.h" />
		<Unit filename="lv_demos/src/lv_demo_keypad_encoder/lv_demo_keypad_encoder.h" />
		<Unit filename="lv_demos/src/lv_demo_music/assets/spectrum_1.h" />
		<Unit filename="lv_demos/src/lv_demo_music/assets/spectrum_2.h" />
		<Unit filename="lv_demos/src/lv_demo_music/assets/spectrum_3.h" />
		<Unit filename="lv_demos/src/lv_demo_music/lv_demo_music.h" />
		<Unit filename="lv_demos/src/lv_demo_music/lv_demo_music_list.h" />
		<Unit filename="lv_demos/src/lv_demo_music/lv_demo_music_main.h" />
		<Unit filename="lv_demos/src/lv_demo_stress/lv_demo_stress.h" />
		<Unit filename="lv_demos/src/lv_demo_widgets/lv_demo_widgets.h" />
		<Unit filename="lv_drivers/display/ILI9341.h" />
		<Unit filename="lv_drivers/display/R61581.h" />
		<Unit filename="lv_drivers/display/SHARP_MIP.h" />
		<Unit filename="lv_drivers/display/SSD1963.h" />
		<Unit filename="lv_drivers/display/ST7565.h" />
		<Unit filename="lv_drivers/display/UC1610.h" />
		<Unit filename="lv_drivers/display/fbdev.h" />
		<Unit filename="lv_drivers/display/monitor.h" />
		<Unit filename="lv_drivers/gtkdrv/gtkdrv.h" />
		<Unit filename="lv_drivers/indev/AD_touch.h" />
		<Unit filename="lv_drivers/indev/FT5406EE8.h" />
		<Unit filename="lv_drivers/indev/XPT2046.h" />
		<Unit filename="lv_drivers/indev/evdev.h" />
		<Unit filename="lv_drivers/indev/keyboard.h" />
		<Unit filename="lv_drivers/indev/libinput_drv.h" />
		<Unit filename="lv_drivers/indev/mouse.h" />
		<Unit filename="lv_drivers/indev/mousewheel.h" />
		<Unit filename="lv_drivers/lv_drv_conf_templ.h" />
		<Unit filename="lv_drivers/lv_drv_conf_template.h" />
		<Unit filename="lv_drivers/win32drv/win32drv.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lv_drivers/win32drv/win32drv.h" />
		<Unit filename="lv_drivers/win_drv.h" />
		<Unit filename="lv_drv_conf.h" />
		<Unit filename="lv_examples/lv_apps/benchmark/benchmark.h" />
		<Unit filename="lv_examples/lv_apps/demo/demo.h" />
		<Unit filename="lv_examples/lv_apps/sysmon/sysmon.h" />
		<Unit filename="lv_examples/lv_apps/terminal/terminal.h" />
		<Unit filename="lv_examples/lv_apps/tpcal/tpcal.h" />
		<Unit filename="lv_examples/lv_ex_conf_templ.h" />
		<Unit filename="lv_examples/lv_examples.h" />
		<Unit filename="lv_examples/lv_tests/lv_test.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_group/lv_test_group.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_misc/lv_test_task.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_obj/lv_test_obj.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_arc/lv_test_arc.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_bar/lv_test_bar.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_btn/lv_test_btn.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_btnm/lv_test_btnm.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_canvas/lv_test_canvas.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_cb/lv_test_cb.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_chart/lv_test_chart.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_cont/lv_test_cont.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_ddlist/lv_test_ddlist.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_gauge/lv_test_gauge.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_img/lv_test_img.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_imgbtn/lv_test_imgbtn.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_kb/lv_test_kb.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_label/lv_test_label.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_led/lv_test_led.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_line/lv_test_line.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_list/lv_test_list.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_lmeter/lv_test_lmeter.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_mbox/lv_test_mbox.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_page/lv_test_page.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_preload/lv_test_preload.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_roller/lv_test_roller.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_slider/lv_test_slider.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_sw/lv_test_sw.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_ta/lv_test_ta.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_table/lv_test_table.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_tabview/lv_test_tabview.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_tileview/lv_test_tileview.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_objx/lv_test_win/lv_test_win.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_stress/lv_test_stress.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_theme/lv_test_theme.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_theme/lv_test_theme_1.h" />
		<Unit filename="lv_examples/lv_tests/lv_test_theme/lv_test_theme_2.h" />
		<Unit filename="lv_examples/lv_tutorial/0_porting/lv_tutorial_porting.h" />
		<Unit filename="lv_examples/lv_tutorial/10_keyboard/lv_tutorial_keyboard.h" />
		<Unit filename="lv_examples/lv_tutorial/1_hello_world/lv_tutorial_hello_world.h" />
		<Unit filename="lv_examples/lv_tutorial/2_objects/lv_tutorial_objects.h" />
		<Unit filename="lv_examples/lv_tutorial/3_styles/lv_tutorial_styles.h" />
		<Unit filename="lv_examples/lv_tutorial/4_themes/lv_tutorial_themes.h" />
		<Unit filename="lv_examples/lv_tutorial/5_antialiasing/lv_tutorial_antialiasing.h" />
		<Unit filename="lv_examples/lv_tutorial/6_images/lv_tutorial_images.h" />
		<Unit filename="lv_examples/lv_tutorial/7_fonts/lv_tutorial_fonts.h" />
		<Unit filename="lv_examples/lv_tutorial/8_animations/lv_tutorial_animations.h" />
		<Unit filename="lv_examples/lv_tutorial/9_responsive/lv_tutorial_responsive.h" />
		<Unit filename="lv_examples/src/lv_demo.h" />
		<Unit filename="lv_examples/src/lv_demo_benchmark/lv_demo_benchmark.h" />
		<Unit filename="lv_examples/src/lv_demo_keypad_encoder/lv_demo_keypad_encoder.h" />
		<Unit filename="lv_examples/src/lv_demo_music/assets/spectrum_1.h" />
		<Unit filename="lv_examples/src/lv_demo_music/assets/spectrum_2.h" />
		<Unit filename="lv_examples/src/lv_demo_music/assets/spectrum_3.h" />
		<Unit filename="lv_examples/src/lv_demo_music/lv_demo_music.h" />
		<Unit filename="lv_examples/src/lv_demo_music/lv_demo_music_list.h" />
		<Unit filename="lv_examples/src/lv_demo_music/lv_demo_music_main.h" />
		<Unit filename="lv_examples/src/lv_demo_printer/lv_demo_printer.h" />
		<Unit filename="lv_examples/src/lv_demo_printer/lv_demo_printer_theme.h" />
		<Unit filename="lv_examples/src/lv_demo_stress/lv_demo_stress.h" />
		<Unit filename="lv_examples/src/lv_demo_widgets/lv_demo_widgets.h" />
		<Unit filename="lv_examples/src/lv_ex_get_started/lv_ex_get_started.h" />
		<Unit filename="lv_examples/src/lv_ex_style/lv_ex_style.h" />
		<Unit filename="lv_examples/src/lv_ex_widgets/lv_ex_widgets.h" />
		<Unit filename="lv_examples/src/lv_examples.h" />
		<Unit filename="lvgl/demos/benchmark/assets/img_benchmark_cogwheel_alpha16.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/assets/img_benchmark_cogwheel_argb.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/assets/img_benchmark_cogwheel_chroma_keyed.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/assets/img_benchmark_cogwheel_indexed16.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/assets/img_benchmark_cogwheel_rgb.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/assets/img_benchmark_cogwheel_rgb565a8.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/assets/lv_font_bechmark_montserrat_12_compr_az.c.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/assets/lv_font_bechmark_montserrat_16_compr_az.c.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/assets/lv_font_bechmark_montserrat_28_compr_az.c.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/lv_demo_benchmark.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/benchmark/lv_demo_benchmark.h" />
		<Unit filename="lvgl/demos/keypad_encoder/lv_demo_keypad_encoder.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/keypad_encoder/lv_demo_keypad_encoder.h" />
		<Unit filename="lvgl/demos/lv_demos.h" />
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_corner_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_list_pause.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_list_pause_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_list_play.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_list_play_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_loop.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_loop_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_next.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_next_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_pause.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_pause_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_play.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_play_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_prev.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_prev_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_rnd.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_btn_rnd_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_corner_left.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_corner_left_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_corner_right.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_corner_right_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_cover_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_cover_1_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_cover_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_cover_2_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_cover_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_cover_3_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_icon_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_icon_1_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_icon_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_icon_2_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_icon_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_icon_3_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_icon_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_icon_4_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_list_border.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_list_border_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_logo.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_slider_knob.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_slider_knob_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_wave_bottom.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_wave_bottom_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_wave_top.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/img_lv_demo_music_wave_top_large.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/assets/spectrum_1.h" />
		<Unit filename="lvgl/demos/music/assets/spectrum_2.h" />
		<Unit filename="lvgl/demos/music/assets/spectrum_3.h" />
		<Unit filename="lvgl/demos/music/lv_demo_music.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/lv_demo_music.h" />
		<Unit filename="lvgl/demos/music/lv_demo_music_list.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/lv_demo_music_list.h" />
		<Unit filename="lvgl/demos/music/lv_demo_music_main.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/music/lv_demo_music_main.h" />
		<Unit filename="lvgl/demos/stress/lv_demo_stress.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/stress/lv_demo_stress.h" />
		<Unit filename="lvgl/demos/widgets/assets/img_clothes.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/widgets/assets/img_demo_widgets_avatar.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/widgets/assets/img_lvgl_logo.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/widgets/lv_demo_widgets.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/demos/widgets/lv_demo_widgets.h" />
		<Unit filename="lvgl/examples/anim/lv_example_anim.h" />
		<Unit filename="lvgl/examples/anim/lv_example_anim_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/anim/lv_example_anim_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/anim/lv_example_anim_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/anim/lv_example_anim_timeline_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/animimg001.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/animimg002.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/animimg003.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/emoji/img_emoji_F617.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/img_caret_down.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/img_cogwheel_alpha16.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/img_cogwheel_argb.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/img_cogwheel_chroma_keyed.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/img_cogwheel_indexed16.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/img_cogwheel_rgb.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/img_hand.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/img_skew_strip.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/img_star.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/imgbtn_left.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/imgbtn_mid.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/assets/imgbtn_right.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/event/lv_example_event.h" />
		<Unit filename="lvgl/examples/event/lv_example_event_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/event/lv_example_event_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/event/lv_example_event_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/event/lv_example_event_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/get_started/lv_example_get_started.h" />
		<Unit filename="lvgl/examples/get_started/lv_example_get_started_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/get_started/lv_example_get_started_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/get_started/lv_example_get_started_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/flex/lv_example_flex.h" />
		<Unit filename="lvgl/examples/layouts/flex/lv_example_flex_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/flex/lv_example_flex_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/flex/lv_example_flex_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/flex/lv_example_flex_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/flex/lv_example_flex_5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/flex/lv_example_flex_6.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/grid/lv_example_grid.h" />
		<Unit filename="lvgl/examples/layouts/grid/lv_example_grid_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/grid/lv_example_grid_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/grid/lv_example_grid_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/grid/lv_example_grid_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/grid/lv_example_grid_5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/grid/lv_example_grid_6.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/layouts/lv_example_layout.h" />
		<Unit filename="lvgl/examples/libs/bmp/lv_example_bmp.h" />
		<Unit filename="lvgl/examples/libs/bmp/lv_example_bmp_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/ffmpeg/lv_example_ffmpeg.h" />
		<Unit filename="lvgl/examples/libs/ffmpeg/lv_example_ffmpeg_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/ffmpeg/lv_example_ffmpeg_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/freetype/lv_example_freetype.h" />
		<Unit filename="lvgl/examples/libs/freetype/lv_example_freetype_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/gif/img_bulb_gif.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/gif/lv_example_gif.h" />
		<Unit filename="lvgl/examples/libs/gif/lv_example_gif_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/lv_example_libs.h" />
		<Unit filename="lvgl/examples/libs/png/img_wink_png.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/png/lv_example_png.h" />
		<Unit filename="lvgl/examples/libs/png/lv_example_png_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/qrcode/lv_example_qrcode.h" />
		<Unit filename="lvgl/examples/libs/qrcode/lv_example_qrcode_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/rlottie/lv_example_rlottie.h" />
		<Unit filename="lvgl/examples/libs/rlottie/lv_example_rlottie_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/rlottie/lv_example_rlottie_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/rlottie/lv_example_rlottie_approve.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/libs/sjpg/lv_example_sjpg.h" />
		<Unit filename="lvgl/examples/libs/sjpg/lv_example_sjpg_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/lv_examples.h" />
		<Unit filename="lvgl/examples/others/fragment/lv_example_fragment.h" />
		<Unit filename="lvgl/examples/others/fragment/lv_example_fragment_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/fragment/lv_example_fragment_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/gridnav/lv_example_gridnav.h" />
		<Unit filename="lvgl/examples/others/gridnav/lv_example_gridnav_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/gridnav/lv_example_gridnav_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/gridnav/lv_example_gridnav_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/gridnav/lv_example_gridnav_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/ime/lv_example_ime_pinyin.h" />
		<Unit filename="lvgl/examples/others/ime/lv_example_ime_pinyin_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/ime/lv_example_ime_pinyin_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/imgfont/lv_example_imgfont.h" />
		<Unit filename="lvgl/examples/others/imgfont/lv_example_imgfont_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/lv_example_others.h" />
		<Unit filename="lvgl/examples/others/monkey/lv_example_monkey.h" />
		<Unit filename="lvgl/examples/others/monkey/lv_example_monkey_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/monkey/lv_example_monkey_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/monkey/lv_example_monkey_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/msg/lv_example_msg.h" />
		<Unit filename="lvgl/examples/others/msg/lv_example_msg_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/msg/lv_example_msg_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/msg/lv_example_msg_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/others/snapshot/lv_example_snapshot.h" />
		<Unit filename="lvgl/examples/others/snapshot/lv_example_snapshot_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/porting/lv_port_disp_template.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/porting/lv_port_disp_template.h" />
		<Unit filename="lvgl/examples/porting/lv_port_fs_template.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/porting/lv_port_fs_template.h" />
		<Unit filename="lvgl/examples/porting/lv_port_indev_template.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/porting/lv_port_indev_template.h" />
		<Unit filename="lvgl/examples/scroll/lv_example_scroll.h" />
		<Unit filename="lvgl/examples/scroll/lv_example_scroll_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/scroll/lv_example_scroll_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/scroll/lv_example_scroll_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/scroll/lv_example_scroll_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/scroll/lv_example_scroll_5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/scroll/lv_example_scroll_6.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style.h" />
		<Unit filename="lvgl/examples/styles/lv_example_style_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_10.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_11.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_12.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_13.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_14.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_15.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_6.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_7.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_8.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/styles/lv_example_style_9.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/animimg/lv_example_animimg_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/arc/lv_example_arc_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/arc/lv_example_arc_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/bar/lv_example_bar_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/bar/lv_example_bar_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/bar/lv_example_bar_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/bar/lv_example_bar_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/bar/lv_example_bar_5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/bar/lv_example_bar_6.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/btn/lv_example_btn_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/btn/lv_example_btn_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/btn/lv_example_btn_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/calendar/lv_example_calendar_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/canvas/lv_example_canvas_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/canvas/lv_example_canvas_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/chart/lv_example_chart_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/chart/lv_example_chart_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/chart/lv_example_chart_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/chart/lv_example_chart_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/chart/lv_example_chart_5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/chart/lv_example_chart_6.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/chart/lv_example_chart_7.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/chart/lv_example_chart_8.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/chart/lv_example_chart_9.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/checkbox/lv_example_checkbox_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/checkbox/lv_example_checkbox_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/colorwheel/lv_example_colorwheel_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/dropdown/lv_example_dropdown_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/dropdown/lv_example_dropdown_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/dropdown/lv_example_dropdown_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/img/lv_example_img_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/img/lv_example_img_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/img/lv_example_img_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/img/lv_example_img_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/imgbtn/lv_example_imgbtn_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/keyboard/lv_example_keyboard_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/label/lv_example_label_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/label/lv_example_label_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/label/lv_example_label_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/label/lv_example_label_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/label/lv_example_label_5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/led/lv_example_led_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/line/lv_example_line_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/list/lv_example_list_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/list/lv_example_list_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/lv_example_widgets.h" />
		<Unit filename="lvgl/examples/widgets/menu/lv_example_menu_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/menu/lv_example_menu_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/menu/lv_example_menu_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/menu/lv_example_menu_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/menu/lv_example_menu_5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/meter/lv_example_meter_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/meter/lv_example_meter_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/meter/lv_example_meter_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/meter/lv_example_meter_4.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/msgbox/lv_example_msgbox_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/obj/lv_example_obj_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/obj/lv_example_obj_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/roller/lv_example_roller_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/roller/lv_example_roller_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/roller/lv_example_roller_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/slider/lv_example_slider_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/slider/lv_example_slider_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/slider/lv_example_slider_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/span/lv_example_span_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/spinbox/lv_example_spinbox_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/spinner/lv_example_spinner_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/switch/lv_example_switch_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/table/lv_example_table_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/table/lv_example_table_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/tabview/lv_example_tabview_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/tabview/lv_example_tabview_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/textarea/lv_example_textarea_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/textarea/lv_example_textarea_2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/textarea/lv_example_textarea_3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/tileview/lv_example_tileview_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/examples/widgets/win/lv_example_win_1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/lv_conf_template.h" />
		<Unit filename="lvgl/lvgl.h" />
		<Unit filename="lvgl/porting/lv_port_disp_template.h" />
		<Unit filename="lvgl/porting/lv_port_fs_template.h" />
		<Unit filename="lvgl/porting/lv_port_indev_template.h" />
		<Unit filename="lvgl/src/core/lv_disp.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_disp.h" />
		<Unit filename="lvgl/src/core/lv_event.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_event.h" />
		<Unit filename="lvgl/src/core/lv_group.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_group.h" />
		<Unit filename="lvgl/src/core/lv_indev.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_indev.h" />
		<Unit filename="lvgl/src/core/lv_indev_scroll.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_indev_scroll.h" />
		<Unit filename="lvgl/src/core/lv_obj.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_obj.h" />
		<Unit filename="lvgl/src/core/lv_obj_class.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_obj_class.h" />
		<Unit filename="lvgl/src/core/lv_obj_draw.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_obj_draw.h" />
		<Unit filename="lvgl/src/core/lv_obj_pos.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_obj_pos.h" />
		<Unit filename="lvgl/src/core/lv_obj_scroll.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_obj_scroll.h" />
		<Unit filename="lvgl/src/core/lv_obj_style.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_obj_style.h" />
		<Unit filename="lvgl/src/core/lv_obj_style_gen.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_obj_style_gen.h" />
		<Unit filename="lvgl/src/core/lv_obj_tree.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_obj_tree.h" />
		<Unit filename="lvgl/src/core/lv_refr.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_refr.h" />
		<Unit filename="lvgl/src/core/lv_theme.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/core/lv_theme.h" />
		<Unit filename="lvgl/src/draw/arm2d/lv_gpu_arm2d.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/arm2d/lv_gpu_arm2d.h" />
		<Unit filename="lvgl/src/draw/lv_draw.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw.h" />
		<Unit filename="lvgl/src/draw/lv_draw_arc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw_arc.h" />
		<Unit filename="lvgl/src/draw/lv_draw_blend.h" />
		<Unit filename="lvgl/src/draw/lv_draw_img.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw_img.h" />
		<Unit filename="lvgl/src/draw/lv_draw_label.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw_label.h" />
		<Unit filename="lvgl/src/draw/lv_draw_layer.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw_layer.h" />
		<Unit filename="lvgl/src/draw/lv_draw_line.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw_line.h" />
		<Unit filename="lvgl/src/draw/lv_draw_mask.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw_mask.h" />
		<Unit filename="lvgl/src/draw/lv_draw_rect.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw_rect.h" />
		<Unit filename="lvgl/src/draw/lv_draw_transform.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw_transform.h" />
		<Unit filename="lvgl/src/draw/lv_draw_triangle.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_draw_triangle.h" />
		<Unit filename="lvgl/src/draw/lv_img_buf.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_img_buf.h" />
		<Unit filename="lvgl/src/draw/lv_img_cache.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_img_cache.h" />
		<Unit filename="lvgl/src/draw/lv_img_decoder.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/lv_img_decoder.h" />
		<Unit filename="lvgl/src/draw/nxp/pxp/lv_draw_pxp.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/pxp/lv_draw_pxp.h" />
		<Unit filename="lvgl/src/draw/nxp/pxp/lv_draw_pxp_blend.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/pxp/lv_draw_pxp_blend.h" />
		<Unit filename="lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp.h" />
		<Unit filename="lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp_osa.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp_osa.h" />
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite.h" />
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.h" />
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite_blend.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite_blend.h" />
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.h" />
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite_rect.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_draw_vglite_rect.h" />
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_vglite_buf.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_vglite_buf.h" />
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_vglite_utils.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/nxp/vglite/lv_vglite_utils.h" />
		<Unit filename="lvgl/src/draw/renesas/lv_gpu_d2_draw_label.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/renesas/lv_gpu_d2_ra6m3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/renesas/lv_gpu_d2_ra6m3.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_arc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_bg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_composite.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_composite.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_img.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_img.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_label.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_layer.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_layer.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_line.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_mask.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_mask.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_polygon.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_priv.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_rect.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_rect.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_stack_blur.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_stack_blur.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_texture_cache.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_texture_cache.h" />
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_utils.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sdl/lv_draw_sdl_utils.h" />
		<Unit filename="lvgl/src/draw/stm32_dma2d/lv_gpu_stm32_dma2d.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/stm32_dma2d/lv_gpu_stm32_dma2d.h" />
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw.h" />
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_arc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_blend.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_blend.h" />
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_dither.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_dither.h" />
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_gradient.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_gradient.h" />
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_img.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_layer.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_letter.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_line.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_polygon.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_rect.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/sw/lv_draw_sw_transform.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/swm341_dma2d/lv_gpu_swm341_dma2d.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/draw/swm341_dma2d/lv_gpu_swm341_dma2d.h" />
		<Unit filename="lvgl/src/extra/layouts/flex/lv_flex.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/layouts/flex/lv_flex.h" />
		<Unit filename="lvgl/src/extra/layouts/grid/lv_grid.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/layouts/grid/lv_grid.h" />
		<Unit filename="lvgl/src/extra/layouts/lv_layouts.h" />
		<Unit filename="lvgl/src/extra/libs/bmp/lv_bmp.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/bmp/lv_bmp.h" />
		<Unit filename="lvgl/src/extra/libs/ffmpeg/lv_ffmpeg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/ffmpeg/lv_ffmpeg.h" />
		<Unit filename="lvgl/src/extra/libs/freetype/lv_freetype.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/freetype/lv_freetype.h" />
		<Unit filename="lvgl/src/extra/libs/fsdrv/lv_fs_fatfs.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/fsdrv/lv_fs_posix.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/fsdrv/lv_fs_stdio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/fsdrv/lv_fs_win32.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/fsdrv/lv_fsdrv.h" />
		<Unit filename="lvgl/src/extra/libs/gif/gifdec.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/gif/gifdec.h" />
		<Unit filename="lvgl/src/extra/libs/gif/lv_gif.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/gif/lv_gif.h" />
		<Unit filename="lvgl/src/extra/libs/lv_libs.h" />
		<Unit filename="lvgl/src/extra/libs/png/lodepng.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/png/lodepng.h" />
		<Unit filename="lvgl/src/extra/libs/png/lv_png.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/png/lv_png.h" />
		<Unit filename="lvgl/src/extra/libs/qrcode/lv_qrcode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/qrcode/lv_qrcode.h" />
		<Unit filename="lvgl/src/extra/libs/qrcode/qrcodegen.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/qrcode/qrcodegen.h" />
		<Unit filename="lvgl/src/extra/libs/rlottie/lv_rlottie.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/rlottie/lv_rlottie.h" />
		<Unit filename="lvgl/src/extra/libs/sjpg/lv_sjpg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/sjpg/lv_sjpg.h" />
		<Unit filename="lvgl/src/extra/libs/sjpg/tjpgd.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/libs/sjpg/tjpgd.h" />
		<Unit filename="lvgl/src/extra/libs/sjpg/tjpgdcnf.h" />
		<Unit filename="lvgl/src/extra/lv_extra.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/lv_extra.h" />
		<Unit filename="lvgl/src/extra/others/fragment/lv_fragment.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/others/fragment/lv_fragment.h" />
		<Unit filename="lvgl/src/extra/others/fragment/lv_fragment_manager.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/others/gridnav/lv_gridnav.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/others/gridnav/lv_gridnav.h" />
		<Unit filename="lvgl/src/extra/others/ime/lv_ime_pinyin.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/others/ime/lv_ime_pinyin.h" />
		<Unit filename="lvgl/src/extra/others/imgfont/lv_imgfont.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/others/imgfont/lv_imgfont.h" />
		<Unit filename="lvgl/src/extra/others/lv_others.h" />
		<Unit filename="lvgl/src/extra/others/monkey/lv_monkey.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/others/monkey/lv_monkey.h" />
		<Unit filename="lvgl/src/extra/others/msg/lv_msg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/others/msg/lv_msg.h" />
		<Unit filename="lvgl/src/extra/others/snapshot/lv_snapshot.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/others/snapshot/lv_snapshot.h" />
		<Unit filename="lvgl/src/extra/themes/basic/lv_theme_basic.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/themes/basic/lv_theme_basic.h" />
		<Unit filename="lvgl/src/extra/themes/default/lv_theme_default.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/themes/default/lv_theme_default.h" />
		<Unit filename="lvgl/src/extra/themes/lv_themes.h" />
		<Unit filename="lvgl/src/extra/themes/mono/lv_theme_mono.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/themes/mono/lv_theme_mono.h" />
		<Unit filename="lvgl/src/extra/widgets/animimg/lv_animimg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/animimg/lv_animimg.h" />
		<Unit filename="lvgl/src/extra/widgets/calendar/lv_calendar.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/calendar/lv_calendar.h" />
		<Unit filename="lvgl/src/extra/widgets/calendar/lv_calendar_header_arrow.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/calendar/lv_calendar_header_arrow.h" />
		<Unit filename="lvgl/src/extra/widgets/calendar/lv_calendar_header_dropdown.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/calendar/lv_calendar_header_dropdown.h" />
		<Unit filename="lvgl/src/extra/widgets/chart/lv_chart.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/chart/lv_chart.h" />
		<Unit filename="lvgl/src/extra/widgets/colorwheel/lv_colorwheel.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/colorwheel/lv_colorwheel.h" />
		<Unit filename="lvgl/src/extra/widgets/imgbtn/lv_imgbtn.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/imgbtn/lv_imgbtn.h" />
		<Unit filename="lvgl/src/extra/widgets/keyboard/lv_keyboard.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/keyboard/lv_keyboard.h" />
		<Unit filename="lvgl/src/extra/widgets/led/lv_led.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/led/lv_led.h" />
		<Unit filename="lvgl/src/extra/widgets/list/lv_list.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/list/lv_list.h" />
		<Unit filename="lvgl/src/extra/widgets/lv_widgets.h" />
		<Unit filename="lvgl/src/extra/widgets/menu/lv_menu.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/menu/lv_menu.h" />
		<Unit filename="lvgl/src/extra/widgets/meter/lv_meter.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/meter/lv_meter.h" />
		<Unit filename="lvgl/src/extra/widgets/msgbox/lv_msgbox.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/msgbox/lv_msgbox.h" />
		<Unit filename="lvgl/src/extra/widgets/span/lv_span.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/span/lv_span.h" />
		<Unit filename="lvgl/src/extra/widgets/spinbox/lv_spinbox.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/spinbox/lv_spinbox.h" />
		<Unit filename="lvgl/src/extra/widgets/spinner/lv_spinner.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/spinner/lv_spinner.h" />
		<Unit filename="lvgl/src/extra/widgets/tabview/lv_tabview.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/tabview/lv_tabview.h" />
		<Unit filename="lvgl/src/extra/widgets/tileview/lv_tileview.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/tileview/lv_tileview.h" />
		<Unit filename="lvgl/src/extra/widgets/win/lv_win.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/extra/widgets/win/lv_win.h" />
		<Unit filename="lvgl/src/font/lv_font.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font.h" />
		<Unit filename="lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_fmt_txt.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_fmt_txt.h" />
		<Unit filename="lvgl/src/font/lv_font_loader.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_loader.h" />
		<Unit filename="lvgl/src/font/lv_font_montserrat_10.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_12.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_12_subpx.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_14.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_16.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_18.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_20.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_22.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_24.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_26.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_28.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_28_compressed.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_30.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_32.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_34.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_36.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_38.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_40.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_42.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_44.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_46.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_48.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_montserrat_8.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_simsun_16_cjk.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_unscii_16.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_font_unscii_8.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/font/lv_symbol_def.h" />
		<Unit filename="lvgl/src/gpu/lv_gpu_nxp_pxp.h" />
		<Unit filename="lvgl/src/gpu/lv_gpu_nxp_pxp_osa.h" />
		<Unit filename="lvgl/src/gpu/lv_gpu_nxp_vglite.h" />
		<Unit filename="lvgl/src/gpu/lv_gpu_sdl.h" />
		<Unit filename="lvgl/src/gpu/lv_gpu_stm32_dma2d.h" />
		<Unit filename="lvgl/src/gpu/sdl/lv_gpu_sdl_lru.h" />
		<Unit filename="lvgl/src/gpu/sdl/lv_gpu_sdl_mask.h" />
		<Unit filename="lvgl/src/gpu/sdl/lv_gpu_sdl_stack_blur.h" />
		<Unit filename="lvgl/src/gpu/sdl/lv_gpu_sdl_texture_cache.h" />
		<Unit filename="lvgl/src/gpu/sdl/lv_gpu_sdl_utils.h" />
		<Unit filename="lvgl/src/hal/lv_hal.h" />
		<Unit filename="lvgl/src/hal/lv_hal_disp.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/hal/lv_hal_disp.h" />
		<Unit filename="lvgl/src/hal/lv_hal_indev.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/hal/lv_hal_indev.h" />
		<Unit filename="lvgl/src/hal/lv_hal_tick.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/hal/lv_hal_tick.h" />
		<Unit filename="lvgl/src/lv_api_map.h" />
		<Unit filename="lvgl/src/lv_conf_checker.h" />
		<Unit filename="lvgl/src/lv_conf_internal.h" />
		<Unit filename="lvgl/src/lv_conf_kconfig.h" />
		<Unit filename="lvgl/src/lv_core/lv_debug.h" />
		<Unit filename="lvgl/src/lv_core/lv_disp.h" />
		<Unit filename="lvgl/src/lv_core/lv_group.h" />
		<Unit filename="lvgl/src/lv_core/lv_indev.h" />
		<Unit filename="lvgl/src/lv_core/lv_obj.h" />
		<Unit filename="lvgl/src/lv_core/lv_obj_style_dec.h" />
		<Unit filename="lvgl/src/lv_core/lv_refr.h" />
		<Unit filename="lvgl/src/lv_core/lv_style.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw_arc.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw_basic.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw_blend.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw_img.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw_label.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw_line.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw_mask.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw_rect.h" />
		<Unit filename="lvgl/src/lv_draw/lv_draw_triangle.h" />
		<Unit filename="lvgl/src/lv_draw/lv_img_buf.h" />
		<Unit filename="lvgl/src/lv_draw/lv_img_cache.h" />
		<Unit filename="lvgl/src/lv_draw/lv_img_decoder.h" />
		<Unit filename="lvgl/src/lv_font/lv_font.h" />
		<Unit filename="lvgl/src/lv_font/lv_font_fmt_txt.h" />
		<Unit filename="lvgl/src/lv_font/lv_font_loader.h" />
		<Unit filename="lvgl/src/lv_font/lv_symbol_def.h" />
		<Unit filename="lvgl/src/lv_fonts/lv_font_builtin.h" />
		<Unit filename="lvgl/src/lv_gpu/lv_gpu_stm32_dma2d.h" />
		<Unit filename="lvgl/src/lv_hal/lv_hal.h" />
		<Unit filename="lvgl/src/lv_hal/lv_hal_disp.h" />
		<Unit filename="lvgl/src/lv_hal/lv_hal_indev.h" />
		<Unit filename="lvgl/src/lv_hal/lv_hal_tick.h" />
		<Unit filename="lvgl/src/lv_misc/lv_anim.h" />
		<Unit filename="lvgl/src/lv_misc/lv_area.h" />
		<Unit filename="lvgl/src/lv_misc/lv_async.h" />
		<Unit filename="lvgl/src/lv_misc/lv_bidi.h" />
		<Unit filename="lvgl/src/lv_misc/lv_circ.h" />
		<Unit filename="lvgl/src/lv_misc/lv_color.h" />
		<Unit filename="lvgl/src/lv_misc/lv_debug.h" />
		<Unit filename="lvgl/src/lv_misc/lv_font.h" />
		<Unit filename="lvgl/src/lv_misc/lv_fs.h" />
		<Unit filename="lvgl/src/lv_misc/lv_gc.h" />
		<Unit filename="lvgl/src/lv_misc/lv_ll.h" />
		<Unit filename="lvgl/src/lv_misc/lv_log.h" />
		<Unit filename="lvgl/src/lv_misc/lv_math.h" />
		<Unit filename="lvgl/src/lv_misc/lv_mem.h" />
		<Unit filename="lvgl/src/lv_misc/lv_printf.h" />
		<Unit filename="lvgl/src/lv_misc/lv_symbol_def.h" />
		<Unit filename="lvgl/src/lv_misc/lv_task.h" />
		<Unit filename="lvgl/src/lv_misc/lv_templ.h" />
		<Unit filename="lvgl/src/lv_misc/lv_txt.h" />
		<Unit filename="lvgl/src/lv_misc/lv_txt_ap.h" />
		<Unit filename="lvgl/src/lv_misc/lv_types.h" />
		<Unit filename="lvgl/src/lv_misc/lv_utils.h" />
		<Unit filename="lvgl/src/lv_objx/lv_arc.h" />
		<Unit filename="lvgl/src/lv_objx/lv_bar.h" />
		<Unit filename="lvgl/src/lv_objx/lv_btn.h" />
		<Unit filename="lvgl/src/lv_objx/lv_btnm.h" />
		<Unit filename="lvgl/src/lv_objx/lv_calendar.h" />
		<Unit filename="lvgl/src/lv_objx/lv_canvas.h" />
		<Unit filename="lvgl/src/lv_objx/lv_cb.h" />
		<Unit filename="lvgl/src/lv_objx/lv_chart.h" />
		<Unit filename="lvgl/src/lv_objx/lv_cont.h" />
		<Unit filename="lvgl/src/lv_objx/lv_cpicker.h" />
		<Unit filename="lvgl/src/lv_objx/lv_ddlist.h" />
		<Unit filename="lvgl/src/lv_objx/lv_gauge.h" />
		<Unit filename="lvgl/src/lv_objx/lv_img.h" />
		<Unit filename="lvgl/src/lv_objx/lv_imgbtn.h" />
		<Unit filename="lvgl/src/lv_objx/lv_kb.h" />
		<Unit filename="lvgl/src/lv_objx/lv_label.h" />
		<Unit filename="lvgl/src/lv_objx/lv_led.h" />
		<Unit filename="lvgl/src/lv_objx/lv_line.h" />
		<Unit filename="lvgl/src/lv_objx/lv_list.h" />
		<Unit filename="lvgl/src/lv_objx/lv_lmeter.h" />
		<Unit filename="lvgl/src/lv_objx/lv_mbox.h" />
		<Unit filename="lvgl/src/lv_objx/lv_objx_templ.h" />
		<Unit filename="lvgl/src/lv_objx/lv_page.h" />
		<Unit filename="lvgl/src/lv_objx/lv_preload.h" />
		<Unit filename="lvgl/src/lv_objx/lv_roller.h" />
		<Unit filename="lvgl/src/lv_objx/lv_slider.h" />
		<Unit filename="lvgl/src/lv_objx/lv_spinbox.h" />
		<Unit filename="lvgl/src/lv_objx/lv_sw.h" />
		<Unit filename="lvgl/src/lv_objx/lv_ta.h" />
		<Unit filename="lvgl/src/lv_objx/lv_table.h" />
		<Unit filename="lvgl/src/lv_objx/lv_tabview.h" />
		<Unit filename="lvgl/src/lv_objx/lv_tileview.h" />
		<Unit filename="lvgl/src/lv_objx/lv_win.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_alien.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_default.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_empty.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_material.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_mono.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_nemo.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_night.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_templ.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_template.h" />
		<Unit filename="lvgl/src/lv_themes/lv_theme_zen.h" />
		<Unit filename="lvgl/src/lv_version.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_arc.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_bar.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_btn.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_btnmatrix.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_calendar.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_canvas.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_chart.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_checkbox.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_cont.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_cpicker.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_dropdown.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_gauge.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_img.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_imgbtn.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_keyboard.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_label.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_led.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_line.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_linemeter.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_list.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_msgbox.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_objmask.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_objx_templ.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_page.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_roller.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_slider.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_spinbox.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_spinner.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_switch.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_table.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_tabview.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_textarea.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_tileview.h" />
		<Unit filename="lvgl/src/lv_widgets/lv_win.h" />
		<Unit filename="lvgl/src/lvgl.h" />
		<Unit filename="lvgl/src/misc/lv_anim.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_anim.h" />
		<Unit filename="lvgl/src/misc/lv_anim_timeline.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_anim_timeline.h" />
		<Unit filename="lvgl/src/misc/lv_area.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_area.h" />
		<Unit filename="lvgl/src/misc/lv_assert.h" />
		<Unit filename="lvgl/src/misc/lv_async.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_async.h" />
		<Unit filename="lvgl/src/misc/lv_bidi.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_bidi.h" />
		<Unit filename="lvgl/src/misc/lv_color.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_color.h" />
		<Unit filename="lvgl/src/misc/lv_fs.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_fs.h" />
		<Unit filename="lvgl/src/misc/lv_gc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_gc.h" />
		<Unit filename="lvgl/src/misc/lv_ll.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_ll.h" />
		<Unit filename="lvgl/src/misc/lv_log.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_log.h" />
		<Unit filename="lvgl/src/misc/lv_lru.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_lru.h" />
		<Unit filename="lvgl/src/misc/lv_math.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_math.h" />
		<Unit filename="lvgl/src/misc/lv_mem.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_mem.h" />
		<Unit filename="lvgl/src/misc/lv_printf.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_printf.h" />
		<Unit filename="lvgl/src/misc/lv_style.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_style.h" />
		<Unit filename="lvgl/src/misc/lv_style_gen.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_style_gen.h" />
		<Unit filename="lvgl/src/misc/lv_templ.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_templ.h" />
		<Unit filename="lvgl/src/misc/lv_timer.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_timer.h" />
		<Unit filename="lvgl/src/misc/lv_tlsf.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_tlsf.h" />
		<Unit filename="lvgl/src/misc/lv_txt.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_txt.h" />
		<Unit filename="lvgl/src/misc/lv_txt_ap.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_txt_ap.h" />
		<Unit filename="lvgl/src/misc/lv_types.h" />
		<Unit filename="lvgl/src/misc/lv_utils.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/misc/lv_utils.h" />
		<Unit filename="lvgl/src/widgets/lv_arc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_arc.h" />
		<Unit filename="lvgl/src/widgets/lv_bar.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_bar.h" />
		<Unit filename="lvgl/src/widgets/lv_btn.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_btn.h" />
		<Unit filename="lvgl/src/widgets/lv_btnmatrix.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_btnmatrix.h" />
		<Unit filename="lvgl/src/widgets/lv_canvas.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_canvas.h" />
		<Unit filename="lvgl/src/widgets/lv_checkbox.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_checkbox.h" />
		<Unit filename="lvgl/src/widgets/lv_dropdown.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_dropdown.h" />
		<Unit filename="lvgl/src/widgets/lv_img.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_img.h" />
		<Unit filename="lvgl/src/widgets/lv_label.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_label.h" />
		<Unit filename="lvgl/src/widgets/lv_line.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_line.h" />
		<Unit filename="lvgl/src/widgets/lv_objx_templ.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_objx_templ.h" />
		<Unit filename="lvgl/src/widgets/lv_roller.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_roller.h" />
		<Unit filename="lvgl/src/widgets/lv_slider.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_slider.h" />
		<Unit filename="lvgl/src/widgets/lv_switch.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_switch.h" />
		<Unit filename="lvgl/src/widgets/lv_table.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_table.h" />
		<Unit filename="lvgl/src/widgets/lv_textarea.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="lvgl/src/widgets/lv_textarea.h" />
		<Unit filename="main.c">
			<Option compilerVar="CC" />
		</Unit>
		<Extensions>
			<DoxyBlocks>
				<comment_style block="0" line="0" />
				<doxyfile_project output_language="" />
				<doxyfile_build />
				<doxyfile_warnings />
				<doxyfile_output />
				<doxyfile_dot />
				<general />
			</DoxyBlocks>
			<code_completion>
				<search_path add="E:\Projects\LittlevGL\" />
			</code_completion>
		</Extensions>
	</Project>
</CodeBlocks_project_file>
