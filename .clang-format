# Clang-Format 配置文件（C++）
# 基于 LLVM 官方风格扩展，包含自定义格式化规则

# --------------------------
# 基础配置（语言与风格继承）
# --------------------------
Language: Cpp                  # 指定配置适用的编程语言为 C++
BasedOnStyle: LLVM             # 以 LLVM 官方风格为基础进行扩展/覆盖

# --------------------------
# 缩进与空白控制
# --------------------------
AccessModifierOffset: -4       # 访问修饰符（如 public/private/protected）的缩进偏移量
                               # 负值表示相对于类/结构体声明向左缩进（例如类声明后换行，public: 会比类名缩进少 4 空格）
IndentWidth: 4                 # 常规代码块的缩进宽度（4 个空格，LLVM 默认 2）
TabWidth: 4                    # Tab 键的实际缩进宽度（与 IndentWidth 保持一致，推荐）
ContinuationIndentWidth: 8     # 多行语句（如长参数列表、初始化列表）的续行缩进宽度（8 个空格）

# --------------------------
# 换行与大括号规则（Brace Wrapping）
# --------------------------
BreakBeforeBraces: Custom      # 自定义大括号换行规则（覆盖 LLVM 默认）
BraceWrapping:                 # 具体大括号换行策略（false 表示不换行，true 表示换行）
  AfterCaseLabel: false        # case 标签后不强制换行（例如：case 1: { ... } 不换行）
  AfterClass: false            # 类定义结束后不换行（例如：class A { ... } 后不换行）
  AfterControlStatement: false # 控制语句（if/for/while 等）后不换行（例如：if (...) { ... } 不换行）
  AfterEnum: false             # 枚举定义结束后不换行（例如：enum E { ... } 后不换行）
  AfterFunction: false         # 函数定义结束后不换行（例如：void f() { ... } 后不换行）
  AfterNamespace: false        # 命名空间定义结束后不换行（例如：namespace N { ... } 后不换行）
  AfterStruct: false           # 结构体定义结束后不换行（例如：struct S { ... } 后不换行）
  AfterUnion: false            # 联合体定义结束后不换行（例如：union U { ... } 后不换行）
  AfterExternBlock: false      # extern "C" 块结束后不换行（例如：extern "C" { ... } 后不换行）
  BeforeCatch: false           # catch 块前不换行（例如：try { ... } catch (...) { ... } 不换行）
  BeforeElse: false            # else 块前不换行（例如：if (...) { ... } else { ... } 不换行）
  BeforeLambdaBody: false      # Lambda 表达式体前不换行（例如：[](){ ... } 不换行）
  BeforeWhile: false           # while 条件前不换行（例如：while (...) { ... } 不换行）
  SplitEmptyFunction: true     # 空函数体强制换行（例如：void f() {} 改为 void f() \n {}）
  SplitEmptyRecord: true       # 空结构体/类/联合体强制换行（例如：struct S {}; 改为 struct S \n {};）
  SplitEmptyNamespace: true    # 空命名空间强制换行（例如：namespace N {}; 改为 namespace N \n {};）

BreakConstructorInitializers: AfterColon       # 构造函数初始化列表在冒号后换行（例如：A() : a(1), b(2) { ... }）
BreakConstructorInitializersBeforeComma: false # 初始化列表逗号前不换行（保持逗号在同一行）

# --------------------------
# 对齐规则（Alignment）
# --------------------------
AlignConsecutiveAssignments: false       # 不对齐连续的赋值语句（例如：int a=1; int b=2; 不对齐为 a=1;  b=2;）
AlignConsecutiveDeclarations: false      # 不对齐连续的变量声明（例如：int a; int b; 不对齐为 a;  b;）
AlignOperands: true                      # 对齐二元运算符的操作数（例如：int x = a + b;  int y = c + d; 对齐为 x = a + b;  y = c + d;）
AlignTrailingComments: true              # 对齐行尾注释（例如：int a; // 变量a  int b; // 变量b 对齐为 a; // 变量a  b; // 变量b）

# --------------------------
# 短语句换行规则
# --------------------------
AllowShortBlocksOnASingleLine: false     # 短代码块（如空块、单行循环体）不允许放在同一行（例如：{} 必须单独一行）
AllowShortFunctionsOnASingleLine: None   # 所有短函数（如 lambda、空函数）都不允许放在同一行（LLVM 默认允许简单函数）
AllowShortIfStatementsOnASingleLine: false # 短 if 语句不允许放在同一行（例如：if (...) return; 必须换行）

# --------------------------
# 空格规则（Spaces）
# --------------------------
SpaceAfterCStyleCast: true               # C 风格类型转换后加空格（例如：(int) x → (int) x）
SpaceAfterTemplateKeyword: false         # template 关键字后不加空格（例如：template<typename T> 不变为 template<typename T>）
SpaceBeforeRangeBasedForLoopColon: false # 范围 for 循环的冒号前不加空格（例如：for (auto& x : vec) 不变为 for (auto& x : vec)）
SpaceInEmptyParentheses: false           # 空括号内不加空格（例如：void f() 而非 void f( )）
SpacesInAngles: false                    # 尖括号（如容器类型）内不加空格（例如：vector<int> 而非 vector< int >）
SpacesInConditionalStatement: false      # 条件语句（if/switch）的括号内不加空格（例如：if (x) 而非 if ( x )）
SpacesInCStyleCastParentheses: false     # C 风格转换的括号内不加空格（例如：(int)x 而非 ( int )x）
SpacesInParentheses: false               # 普通括号（函数调用、表达式）内不加空格（例如：f(x) 而非 f( x )）

# --------------------------
# 其他实用规则
# --------------------------
AlwaysBreakTemplateDeclarations: Yes     # 模板声明强制换行（例如：template<typename T> class A; 必须换行）
ColumnLimit: 120                         # 单行最大字符数限制（120，LLVM 默认 80）
ConstructorInitializerAllOnOneLineOrOnePerLine: false # 构造函数初始化列表不强制单行或每行一个成员（灵活处理）
IncludeCategories:                       # 头文件包含顺序优先级（按正则匹配排序）
  - Regex: '^<.*'                        # 优先级 1：系统头文件（<> 包裹）
    Priority: 1
  - Regex: '^".*"'                       # 优先级 2：本地头文件（"" 包裹）
    Priority: 2
  - Regex: '.*'                          # 优先级 3：其他头文件（无明确匹配）
    Priority: 3
IncludeIsMainRegex: '([-_](test|unittest))?$' # 判断主文件的正则（匹配含 test/unittest 后缀的文件，如 main_test.cpp 视为主文件）
IndentCaseLabels: true                   # case 标签缩进（例如：switch (x) { case 1: ... } 中 case 1: 缩进）
#IndentNamespaceContents: All             # 命名空间内容缩进（例如：namespace N { int x; } 中 x 缩进，LLVM 默认不缩进）
InsertNewlineAtEOF: true                 # 文件末尾强制插入换行符（符合 POSIX 规范）
MaxEmptyLinesToKeep: 2                   # 最多保留 2 个连续空行（超过则合并，LLVM 默认 1）
MacroBlockBegin: ''                      # 宏块开始标记（空表示不识别特殊宏块）
MacroBlockEnd: ''                        # 宏块结束标记（空表示不识别特殊宏块）
AllowShortLoopsOnASingleLine: true       # 允许短循环（包括空循环）保持在同一行