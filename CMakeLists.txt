set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_SYSTEM_VERSION 1)
cmake_minimum_required(VERSION 3.28)

set(CMAKE_C_COMPILER gcc)
set(CMAKE_CXX_COMPILER g++)
set(CMAKE_ASM_COMPILER gcc)
#set(CMAKE_AR arm-none-eabi-ar)
#set(CMAKE_OBJCOPY arm-none-eabi-objcopy)
#set(CMAKE_OBJDUMP arm-none-eabi-objdump)
#set(SIZE arm-none-eabi-size)
set(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)

# 添加预处理器定义
#add_definitions(-DAPIENTRY=__stdcall)
add_definitions(-DSDL_MAIN_HANDLED)

project(UI_QZ_N)

set(CMAKE_CXX_STANDARD 14)

include_directories(
        lvgl
        View/Inc
        ViewModel/Inc
        View/widget
)

file(GLOB_RECURSE SOURCES "lvgl/*.*" "View/*.*" "main.c" "lv_conf.h" "ViewModel/*.*")

# 手动添加主文件
set(MAIN_FILE main.c)

#set_target_properties(${PROJ_NAME} PROPERTIES LINK_FLAGS "/SUBSYSTEM:WINDOWS")
#target_link_libraries(${PROJECT_NAME} PUBLIC WinMain)

# 添加可执行文件
add_executable(${PROJECT_NAME} ${SOURCES})