//
// Created by <PERSON><PERSON> on 2024/10/25.
//

#ifndef QZ_N_VIEW_MODEL_H
#define QZ_N_VIEW_MODEL_H

#include "lvgl.h"
#include "user.h"

void defocused_handle(lv_group_t *group);

void reset_key_long_press_state();

void sync_param_battery();

void send_scan_ch_finished();

void send_zoom_data();

void set_start_scan_ch();

void send_param_to_cc2500();

lv_obj_t *get_page_first_focus_obj();

void set_screen_brightness();

void set_flash_mode_level();

void set_zoom_level(uint8_t group_name);

void set_beep_lamp_level();

void set_multi_param();

void send_sync_status();

void send_lock_maks_long_pressed_event();

void send_lock_maks_released_event();

void set_reset();

void set_RF_status();

bool get_camera_link();

uint8_t set_flash_level_max();

void send_camera_link_status();

void set_device_off();

void set_device_restart();

bool check_timer_is_run(lv_timer_t *timer);

void send_create_sleep_page();

void send_del_sleep_page();

#endif //QZ_N_VIEW_MODEL_H
